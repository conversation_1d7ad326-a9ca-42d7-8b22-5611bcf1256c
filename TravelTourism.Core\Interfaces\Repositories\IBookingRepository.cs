using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TravelTourism.Core.Entities.Booking;

namespace TravelTourism.Core.Interfaces.Repositories
{
    public interface IBookingRepository : IGenericRepository<Booking>
    {
        IQueryable<Booking> GetQueryable();
        Task<IReadOnlyList<Booking>> GetUserBookingsAsync(int userId);
        Task<Booking> GetByBookingNumberAsync(string bookingNumber);
        Task<IReadOnlyList<Booking>> GetTripBookingsAsync(int tripId);
        Task<Booking> GetBookingWithDetailsAsync(int id);
        Task<int> GetTripBookedCapacityAsync(int tripId, System.DateTime travelDate);
        Task<bool> IsBookingNumberUniqueAsync(string bookingNumber);
    }
}
