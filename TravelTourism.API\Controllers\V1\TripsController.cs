using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.Trip;
using TravelTourism.Application.Interfaces;

namespace TravelTourism.API.Controllers.V1
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class TripsController : BaseController
    {
        private readonly ITripService _tripService;

        public TripsController(ITripService tripService)
        {
            _tripService = tripService;
        }

        /// <summary>
        /// Get paginated list of trips with optional filtering
        /// </summary>
        /// <param name="filter">Trip filter criteria</param>
        /// <param name="pagination">Pagination parameters</param>
        /// <returns>Paginated trips list</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<PagedResult<TripDto>>), 200)]
        public async Task<IActionResult> GetTrips([FromQuery] TripFilterDto filter, [FromQuery] PaginationParameters pagination)
        {
            var result = await _tripService.GetTripsAsync(filter, pagination);
            return HandleResult(result);
        }

        /// <summary>
        /// Get trip details by ID
        /// </summary>
        /// <param name="id">Trip ID</param>
        /// <returns>Trip details</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(ApiResponse<TripDetailDto>), 200)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> GetTrip(int id)
        {
            var result = await _tripService.GetTripByIdAsync(id);
            return HandleResult(result);
        }

        /// <summary>
        /// Get featured trips
        /// </summary>
        /// <param name="count">Number of featured trips to return (default: 10)</param>
        /// <returns>List of featured trips</returns>
        [HttpGet("featured")]
        [ProducesResponseType(typeof(ApiResponse<List<TripDto>>), 200)]
        public async Task<IActionResult> GetFeaturedTrips([FromQuery] int count = 10)
        {
            var result = await _tripService.GetFeaturedTripsAsync(count);
            return HandleResult(result);
        }

        /// <summary>
        /// Get all trip categories
        /// </summary>
        /// <returns>List of trip categories</returns>
        [HttpGet("categories")]
        [ProducesResponseType(typeof(ApiResponse<List<TripCategoryDto>>), 200)]
        public async Task<IActionResult> GetCategories()
        {
            var result = await _tripService.GetCategoriesAsync();
            return HandleResult(result);
        }

        /// <summary>
        /// Get all available destinations
        /// </summary>
        /// <returns>List of destination cities</returns>
        [HttpGet("destinations")]
        [ProducesResponseType(typeof(ApiResponse<List<CityDto>>), 200)]
        public async Task<IActionResult> GetDestinations()
        {
            var result = await _tripService.GetDestinationsAsync();
            return HandleResult(result);
        }

        /// <summary>
        /// Get trips by category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <returns>List of trips in the category</returns>
        [HttpGet("category/{categoryId}")]
        [ProducesResponseType(typeof(ApiResponse<List<TripDto>>), 200)]
        public async Task<IActionResult> GetTripsByCategory(int categoryId)
        {
            var result = await _tripService.GetTripsByCategoryAsync(categoryId);
            return HandleResult(result);
        }

        /// <summary>
        /// Get trips by destination
        /// </summary>
        /// <param name="destinationId">Destination city ID</param>
        /// <returns>List of trips to the destination</returns>
        [HttpGet("destination/{destinationId}")]
        [ProducesResponseType(typeof(ApiResponse<List<TripDto>>), 200)]
        public async Task<IActionResult> GetTripsByDestination(int destinationId)
        {
            var result = await _tripService.GetTripsByDestinationAsync(destinationId);
            return HandleResult(result);
        }

        /// <summary>
        /// Search trips
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="categoryId">Optional category filter</param>
        /// <param name="destinationId">Optional destination filter</param>
        /// <returns>List of matching trips</returns>
        [HttpGet("search")]
        [ProducesResponseType(typeof(ApiResponse<List<TripDto>>), 200)]
        public async Task<IActionResult> SearchTrips(
            [FromQuery] string searchTerm,
            [FromQuery] int? categoryId = null,
            [FromQuery] int? destinationId = null)
        {
            var result = await _tripService.SearchTripsAsync(searchTerm, categoryId, destinationId);
            return HandleResult(result);
        }

        /// <summary>
        /// Check if a trip is available for booking
        /// </summary>
        /// <param name="tripId">Trip ID</param>
        /// <param name="travelDate">Travel date</param>
        /// <param name="numberOfPeople">Number of people</param>
        /// <returns>Availability status</returns>
        [HttpGet("{tripId}/availability")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        public async Task<IActionResult> CheckAvailability(int tripId, [FromQuery] DateTime travelDate, [FromQuery] int numberOfPeople)
        {
            var result = await _tripService.IsTripAvailableAsync(tripId, travelDate, numberOfPeople);
            return HandleResult(result);
        }
    }
}
