namespace TravelTourism.Application.DTOs.Admin
{
    public class ExportInvoicesRequest
    {
        public string Format { get; set; } = "pdf"; // "pdf", "excel", "csv"
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int? BookingId { get; set; }
        public int? UserId { get; set; }
        public string? Status { get; set; }
        public bool IncludePaid { get; set; } = true;
        public bool IncludeUnpaid { get; set; } = true;
    }
} 