namespace TravelTourism.Application.DTOs.Admin;

public class DashboardStatsDto
{
    public int TotalUsers { get; set; }
    public int TotalTrips { get; set; }
    public int TotalBookings { get; set; }
    public int TotalBlogs { get; set; }
    public decimal TotalRevenue { get; set; }
    public int PendingBookings { get; set; }
    public int ConfirmedBookings { get; set; }
    public int CancelledBookings { get; set; }
    public int ActiveTrips { get; set; }
    public int FeaturedTrips { get; set; }
    public int PublishedBlogs { get; set; }
    public int FeaturedBlogs { get; set; }
    public decimal MonthlyRevenue { get; set; }
    public decimal YearlyRevenue { get; set; }
    public int NewUsersThisMonth { get; set; }
    public int NewBookingsThisMonth { get; set; }
    public int NewTripsThisMonth { get; set; }
    public int NewBlogsThisMonth { get; set; }
    public int CompletedBookings { get; set; }
    public decimal RevenueThisMonth { get; set; }
    public decimal RevenueLastMonth { get; set; }
    public int ActiveUsers { get; set; }
    public int AdminUsers { get; set; }
    public decimal RevenueGrowthPercentage { get; set; }
    public List<ChartDataPoint> RevenueChart { get; set; } = new List<ChartDataPoint>();
    public List<ChartDataPoint> BookingsChart { get; set; } = new List<ChartDataPoint>();
    public List<ChartDataPoint> UsersChart { get; set; } = new List<ChartDataPoint>();
}

public class ChartDataPoint
{
    public string Label { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public int Count { get; set; }
} 