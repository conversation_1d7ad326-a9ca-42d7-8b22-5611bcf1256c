using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using TravelTourism.Application.DTOs.Common;

namespace TravelTourism.API.Controllers.Base
{
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    public abstract class BaseController : ControllerBase
    {
        protected int UserId
        {
            get
            {
                var userIdClaim = User?.FindFirst("userId")?.Value ?? User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                return int.TryParse(userIdClaim, out var userId) ? userId : 0;
            }
        }

        protected string UserEmail => User?.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty;

        protected string UserRole => User?.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;

        protected bool IsAdmin => UserRole.Equals("Admin", StringComparison.OrdinalIgnoreCase);

        protected IActionResult HandleResult<T>(ApiResponse<T> result)
        {
            if (result.Success)
            {
                return Ok(result);
            }

            if (result.Errors?.Any() == true)
            {
                return BadRequest(result);
            }

            return StatusCode(500, result);
        }

        protected IActionResult HandleResult(ApiResponse result)
        {
            if (result.Success)
            {
                return Ok(result);
            }

            if (result.Errors?.Any() == true)
            {
                return BadRequest(result);
            }

            return StatusCode(500, result);
        }

        protected ApiResponse<T> CreateSuccessResponse<T>(T data, string message = "Success")
        {
            return ApiResponse<T>.SuccessResponse(data, message);
        }

        protected ApiResponse<T> CreateErrorResponse<T>(string message, List<string> errors = null)
        {
            return ApiResponse<T>.ErrorResponse(message, errors);
        }

        protected ApiResponse CreateSuccessResponse(string message = "Success")
        {
            return ApiResponse.SuccessResponse(message);
        }

        protected ApiResponse CreateErrorResponse(string message, List<string> errors = null)
        {
            return ApiResponse.ErrorResponse(message, errors);
        }

        protected ApiResponse CreateErrorResponse(string message, string errorCode)
        {
            return ApiResponse.ErrorResponse(message, new List<string> { errorCode });
        }

        protected ApiResponse CreateValidationErrorResponse(Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary modelState)
        {
            var errors = modelState.Values
                .SelectMany(v => v.Errors)
                .Select(e => e.ErrorMessage)
                .ToList();
            return ApiResponse.ErrorResponse("Validation failed", errors);
        }

        protected int GetCurrentUserId()
        {
            return UserId;
        }

        protected IActionResult HandleException(Exception ex)
        {
            // Log the exception here if you have logging configured
            return StatusCode(500, CreateErrorResponse("An unexpected error occurred", "INTERNAL_SERVER_ERROR"));
        }
    }
}
