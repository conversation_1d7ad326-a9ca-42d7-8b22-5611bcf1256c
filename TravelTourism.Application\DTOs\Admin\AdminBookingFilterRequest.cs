namespace TravelTourism.Application.DTOs.Admin
{
    public class AdminBookingFilterRequest
    {
        public string? SearchTerm { get; set; }
        public int? TripId { get; set; }
        public int? UserId { get; set; }
        public string? Status { get; set; }
        public DateTime? BookingDateFrom { get; set; }
        public DateTime? BookingDateTo { get; set; }
        public DateTime? TripDateFrom { get; set; }
        public DateTime? TripDateTo { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }
} 