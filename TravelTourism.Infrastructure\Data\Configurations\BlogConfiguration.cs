using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TravelTourism.Core.Entities.Blog;

namespace TravelTourism.Infrastructure.Data.Configurations;

public class BlogConfiguration : IEntityTypeConfiguration<Blog>
{
    public void Configure(EntityTypeBuilder<Blog> builder)
    {
        builder.ToTable("Blogs");

        builder.HasKey(b => b.Id);

        builder.Property(b => b.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(b => b.Slug)
            .IsRequired()
            .HasMaxLength(200);

        builder.HasIndex(b => b.Slug)
            .IsUnique();

        builder.Property(b => b.Content)
            .IsRequired();

        builder.Property(b => b.Excerpt)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(b => b.FeaturedImageUrl)
            .HasMaxLength(500);

        builder.Property(b => b.IsPublished)
            .HasDefaultValue(false);

        builder.Property(b => b.IsFeatured)
            .HasDefaultValue(false);

        builder.Property(b => b.IsActive)
            .HasDefaultValue(true);

        builder.Property(b => b.ViewCount)
            .HasDefaultValue(0);

        // Relationships
        builder.HasOne(b => b.Author)
            .WithMany(u => u.Blogs)
            .HasForeignKey(b => b.AuthorId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(b => b.Category)
            .WithMany(c => c.Blogs)
            .HasForeignKey(b => b.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(b => b.Images)
            .WithOne(i => i.Blog)
            .HasForeignKey(i => i.BlogId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}