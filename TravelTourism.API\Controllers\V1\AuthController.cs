using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.DTOs.Auth;
using TravelTourism.Application.Interfaces;

namespace TravelTourism.API.Controllers.V1
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [ApiVersion("1.0")]
    public class AuthController : BaseController
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        /// <summary>
        /// Register a new user account
        /// </summary>
        /// <param name="registerDto">User registration information</param>
        /// <returns>Registration result with user details</returns>
        [HttpPost("register")]
        [ProducesResponseType(typeof(AuthResultDto), 200)]
        [ProducesResponseType(typeof(AuthResultDto), 400)]
        public async Task<IActionResult> Register([FromBody] RegisterDto registerDto)
        {
            var result = await _authService.RegisterAsync(registerDto);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Login with email and password
        /// </summary>
        /// <param name="loginDto">Login credentials</param>
        /// <returns>Authentication result with JWT tokens</returns>
        [HttpPost("login")]
        [ProducesResponseType(typeof(AuthResultDto), 200)]
        [ProducesResponseType(typeof(AuthResultDto), 400)]
        public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
        {
            var result = await _authService.LoginAsync(loginDto);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Verify email address
        /// </summary>
        /// <param name="verifyEmailDto">Email verification details</param>
        /// <returns>Verification result</returns>
        [HttpPost("verify-email")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> VerifyEmail([FromBody] VerifyEmailDto verifyEmailDto)
        {
            var result = await _authService.VerifyEmailAsync(verifyEmailDto);
            return HandleResult(result);
        }

        /// <summary>
        /// Request password reset
        /// </summary>
        /// <param name="forgotPasswordDto">Forgot password request</param>
        /// <returns>Password reset result</returns>
        [HttpPost("forgot-password")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordDto forgotPasswordDto)
        {
            var result = await _authService.ForgotPasswordAsync(forgotPasswordDto);
            return HandleResult(result);
        }

        /// <summary>
        /// Reset password with token
        /// </summary>
        /// <param name="resetPasswordDto">Password reset details</param>
        /// <returns>Password reset result</returns>
        [HttpPost("reset-password")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordDto resetPasswordDto)
        {
            var result = await _authService.ResetPasswordAsync(resetPasswordDto);
            return HandleResult(result);
        }

        /// <summary>
        /// Refresh JWT access token
        /// </summary>
        /// <param name="refreshTokenDto">Refresh token</param>
        /// <returns>New access token</returns>
        [HttpPost("refresh-token")]
        [ProducesResponseType(typeof(AuthResultDto), 200)]
        [ProducesResponseType(typeof(AuthResultDto), 400)]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenDto refreshTokenDto)
        {
            var result = await _authService.RefreshTokenAsync(refreshTokenDto);
            
            if (result.Success)
                return Ok(result);
            
            return BadRequest(result);
        }

        /// <summary>
        /// Logout current user (revoke all tokens)
        /// </summary>
        /// <returns>Logout result</returns>
        [HttpPost("logout")]
        [Authorize]
        [ProducesResponseType(200)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> Logout()
        {
            var result = await _authService.LogoutAsync(UserId);
            return HandleResult(result);
        }

        /// <summary>
        /// Revoke all user tokens (force logout from all devices)
        /// </summary>
        /// <returns>Token revocation result</returns>
        [HttpPost("revoke-all-tokens")]
        [Authorize]
        [ProducesResponseType(200)]
        [ProducesResponseType(401)]
        public async Task<IActionResult> RevokeAllTokens()
        {
            var result = await _authService.RevokeAllUserTokensAsync(UserId);
            return HandleResult(result);
        }
    }
}
