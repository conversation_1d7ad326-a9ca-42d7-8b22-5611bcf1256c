using System.Collections.Generic;

namespace TravelTourism.Core.Exceptions
{
    public class ValidationException : DomainException
    {
        public IReadOnlyList<string> Errors { get; }

        public ValidationException(string message) : base(message)
        {
            Errors = new List<string> { message };
        }

        public ValidationException(IList<string> errors) : base("One or more validation errors occurred.")
        {
            Errors = new List<string>(errors);
        }
    }
}
