using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.TripSpecifications;

public class TripsByDestinationSpecification : BaseSpecification<Trip>
{
    public TripsByDestinationSpecification(int destinationCityId)
        : base(t => t.DestinationCityId == destinationCityId && t.IsActive && !t.IsDeleted)
    {
        AddInclude(t => t.Category);
        AddInclude(t => t.DestinationCity);
        AddInclude(t => t.DepartureCity);
        AddInclude(t => t.TripImages);
        AddOrderBy(t => t.Name);
    }
} 