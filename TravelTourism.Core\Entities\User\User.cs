using System;
using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;
using TravelTourism.Core.Entities.Booking;
using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Enums;

namespace TravelTourism.Core.Entities.User
{
    public class User : BaseEntity
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public Gender? Gender { get; set; }
        public string ProfileImageUrl { get; set; }
        public string AddressLine1 { get; set; }
        public string AddressLine2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
        public string PasswordHash { get; set; }
        public UserRole Role { get; set; } = UserRole.User;
        public bool IsEmailVerified { get; set; } = false;
        public string EmailVerificationToken { get; set; }
        public DateTime? EmailVerificationTokenExpiry { get; set; }
        public string PasswordResetToken { get; set; }
        public DateTime? PasswordResetTokenExpiry { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime? LastLoginAt { get; set; }

        // Navigation properties
        public virtual ICollection<Booking.Booking> Bookings { get; set; } = new List<Booking.Booking>();
        public virtual ICollection<Blog.Blog> Blogs { get; set; } = new List<Blog.Blog>();
        public virtual ICollection<UserToken> UserTokens { get; set; } = new List<UserToken>();

        public string FullName => $"{FirstName} {LastName}".Trim();

        public int Age
        {
            get
            {
                if (!DateOfBirth.HasValue)
                    return 0;

                var today = DateTime.Today;
                var age = today.Year - DateOfBirth.Value.Year;
                if (DateOfBirth.Value.Date > today.AddYears(-age))
                    age--;
                return age;
            }
        }

        public bool IsEmailVerificationTokenValid(string token)
        {
            return !string.IsNullOrEmpty(EmailVerificationToken) &&
                   EmailVerificationToken == token &&
                   EmailVerificationTokenExpiry.HasValue &&
                   EmailVerificationTokenExpiry.Value > DateTime.UtcNow;
        }

        public bool IsPasswordResetTokenValid(string token)
        {
            return !string.IsNullOrEmpty(PasswordResetToken) &&
                   PasswordResetToken == token &&
                   PasswordResetTokenExpiry.HasValue &&
                   PasswordResetTokenExpiry.Value > DateTime.UtcNow;
        }

        public void VerifyEmail()
        {
            IsEmailVerified = true;
            EmailVerificationToken = null;
            EmailVerificationTokenExpiry = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void UpdateLastLogin()
        {
            LastLoginAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
