using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;
using TravelTourism.Application.Models.Requests;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Application.Services
{
    public class FileService : IFileService
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<FileService> _logger;

        public FileService(
            IFileStorageService fileStorageService,
            ILogger<FileService> logger)
        {
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        public async Task<ApiResponse<string>> UploadImageAsync(FileUploadRequest request)
        {
            try
            {
                if (request.File == null || request.File.Length == 0)
                {
                    return ApiResponse<string>.ErrorResponse("No file provided");
                }

                // Validate file type
                if (!IsValidImageFile(request.File))
                {
                    return ApiResponse<string>.ErrorResponse("Invalid file type. Only images are allowed.");
                }

                // Validate file size (max 10MB)
                if (request.File.Length > 10 * 1024 * 1024)
                {
                    return ApiResponse<string>.ErrorResponse("File size too large. Maximum size is 10MB.");
                }

                using var stream = request.File.OpenReadStream();
                var fileName = request.File.FileName;
                var folder = request.Folder ?? "general";

                var fileUrl = await _fileStorageService.UploadImageAsync(stream, fileName, folder);

                _logger.LogInformation("Image uploaded successfully: {FileName} to folder {Folder}", fileName, folder);

                return ApiResponse<string>.SuccessResponse(fileUrl, "Image uploaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image: {FileName}", request.File?.FileName);
                return ApiResponse<string>.ErrorResponse("Failed to upload image", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse<string>> UploadDocumentAsync(FileUploadRequest request)
        {
            try
            {
                if (request.File == null || request.File.Length == 0)
                {
                    return ApiResponse<string>.ErrorResponse("No file provided");
                }

                // Validate file type
                if (!IsValidDocumentFile(request.File))
                {
                    return ApiResponse<string>.ErrorResponse("Invalid file type. Only documents are allowed.");
                }

                // Validate file size (max 50MB)
                if (request.File.Length > 50 * 1024 * 1024)
                {
                    return ApiResponse<string>.ErrorResponse("File size too large. Maximum size is 50MB.");
                }

                using var stream = request.File.OpenReadStream();
                var fileName = request.File.FileName;
                var folder = request.Folder ?? "documents";

                var fileUrl = await _fileStorageService.UploadDocumentAsync(stream, fileName, folder);

                _logger.LogInformation("Document uploaded successfully: {FileName} to folder {Folder}", fileName, folder);

                return ApiResponse<string>.SuccessResponse(fileUrl, "Document uploaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading document: {FileName}", request.File?.FileName);
                return ApiResponse<string>.ErrorResponse("Failed to upload document", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse> DeleteFileAsync(string fileUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(fileUrl))
                {
                    return ApiResponse.ErrorResponse("File URL is required");
                }

                var success = await _fileStorageService.DeleteFileAsync(fileUrl);

                if (success)
                {
                    _logger.LogInformation("File deleted successfully: {FileUrl}", fileUrl);
                    return ApiResponse.SuccessResponse("File deleted successfully");
                }
                else
                {
                    return ApiResponse.ErrorResponse("Failed to delete file");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FileUrl}", fileUrl);
                return ApiResponse.ErrorResponse("Failed to delete file", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse<string>> GetFileUrlAsync(string fileId)
        {
            try
            {
                if (string.IsNullOrEmpty(fileId))
                {
                    return ApiResponse<string>.ErrorResponse("File ID is required");
                }

                var fileUrl = await _fileStorageService.GetFileUrlAsync(fileId);

                if (!string.IsNullOrEmpty(fileUrl))
                {
                    return ApiResponse<string>.SuccessResponse(fileUrl, "File URL retrieved successfully");
                }
                else
                {
                    return ApiResponse<string>.ErrorResponse("File not found");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving file URL: {FileId}", fileId);
                return ApiResponse<string>.ErrorResponse("Failed to retrieve file URL", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse<List<string>>> GetFilesInFolderAsync(string folder)
        {
            try
            {
                if (string.IsNullOrEmpty(folder))
                {
                    return ApiResponse<List<string>>.ErrorResponse("Folder name is required");
                }

                var files = await _fileStorageService.GetFilesInFolderAsync(folder);

                return ApiResponse<List<string>>.SuccessResponse(files, "Files retrieved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving files from folder: {Folder}", folder);
                return ApiResponse<List<string>>.ErrorResponse("Failed to retrieve files", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse<string>> OptimizeImageAsync(string imageUrl, int? width = null, int? height = null, string format = "auto")
        {
            try
            {
                if (string.IsNullOrEmpty(imageUrl))
                {
                    return ApiResponse<string>.ErrorResponse("Image URL is required");
                }

                var optimizedUrl = await _fileStorageService.OptimizeImageAsync(imageUrl, width, height, format);

                if (!string.IsNullOrEmpty(optimizedUrl))
                {
                    return ApiResponse<string>.SuccessResponse(optimizedUrl, "Image optimized successfully");
                }
                else
                {
                    return ApiResponse<string>.ErrorResponse("Failed to optimize image");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing image: {ImageUrl}", imageUrl);
                return ApiResponse<string>.ErrorResponse("Failed to optimize image", new List<string> { ex.Message });
            }
        }

        private bool IsValidImageFile(IFormFile file)
        {
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            return allowedExtensions.Contains(extension);
        }

        private bool IsValidDocumentFile(IFormFile file)
        {
            var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            return allowedExtensions.Contains(extension);
        }
    }
} 