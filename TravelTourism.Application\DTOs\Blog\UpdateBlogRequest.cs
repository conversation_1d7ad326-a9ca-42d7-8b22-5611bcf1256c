using System.ComponentModel.DataAnnotations;

namespace TravelTourism.Application.DTOs.Blog;

public class UpdateBlogRequest
{
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(500)]
    public string Excerpt { get; set; } = string.Empty;
    
    [Required]
    public int CategoryId { get; set; }
    
    public string? FeaturedImageUrl { get; set; }
    public bool IsPublished { get; set; }
    public bool IsFeatured { get; set; }
    public List<string> Tags { get; set; } = new();
} 