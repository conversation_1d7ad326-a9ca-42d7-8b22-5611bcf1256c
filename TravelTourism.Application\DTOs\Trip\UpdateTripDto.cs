using System.ComponentModel.DataAnnotations;
using TravelTourism.Core.Enums;

namespace TravelTourism.Application.DTOs.Trip;

public class UpdateTripDto
{
    public int Id { get; set; }
    
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public string Description { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(500)]
    public string ShortDescription { get; set; } = string.Empty;
    
    [Required]
    [Range(0, double.MaxValue)]
    public decimal Price { get; set; }
    
    [Range(0, double.MaxValue)]
    public decimal? DiscountPrice { get; set; }
    
    [Required]
    [Range(1, 365)]
    public int Duration { get; set; }
    
    [Required]
    [Range(1, 1000)]
    public int MaxCapacity { get; set; }
    
    [Range(0, 120)]
    public int? MinAge { get; set; }
    
    [Range(0, 120)]
    public int? MaxAge { get; set; }
    
    [Required]
    public TripDifficulty Difficulty { get; set; }
    
    [Required]
    public int CategoryId { get; set; }
    
    [Required]
    public int DestinationCityId { get; set; }
    
    [Required]
    public int DepartureCityId { get; set; }
    
    public bool IncludesAccommodation { get; set; }
    public bool IncludesTransport { get; set; }
    public bool IncludesMeals { get; set; }
    public bool IncludesGuide { get; set; }
    
    [Required]
    [MaxLength(500)]
    public string MainImageUrl { get; set; } = string.Empty;
    
    public bool IsFeatured { get; set; }
    
    [Required]
    public DateTime AvailableFrom { get; set; }
    
    [Required]
    public DateTime AvailableTo { get; set; }
    
    public List<string> ImageUrls { get; set; } = new();
    public List<TripItineraryDto> Itineraries { get; set; } = new();
} 