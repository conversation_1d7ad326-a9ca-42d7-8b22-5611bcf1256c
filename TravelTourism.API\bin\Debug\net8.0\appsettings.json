{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/travel-tourism-api-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=TravelTourismDB;Trusted_Connection=true;MultipleActiveResultSets=true"}, "JwtSettings": {"Key": "your-super-secret-key-here-make-it-long-and-complex-at-least-32-characters", "Issuer": "TravelTourismAPI", "Audience": "TravelTourismAPI", "DurationInMinutes": 60}, "EmailSettings": {"ApiKey": "your-sendgrid-api-key", "FromEmail": "<EMAIL>", "FromName": "Travel & Tourism"}, "FileStorage": {"CloudinarySettings": {"CloudName": "your-cloudinary-cloud-name", "ApiKey": "your-cloudinary-api-key", "ApiSecret": "your-cloudinary-api-secret"}}, "PaymentSettings": {"Stripe": {"PublishableKey": "your-stripe-publishable-key", "SecretKey": "your-stripe-secret-key"}}, "Redis": {"ConnectionString": ""}, "RateLimiting": {"EnableRateLimiting": true, "Rules": [{"Endpoint": "*", "Period": "1m", "Limit": 100}, {"Endpoint": "*/auth/*", "Period": "1m", "Limit": 10}]}}