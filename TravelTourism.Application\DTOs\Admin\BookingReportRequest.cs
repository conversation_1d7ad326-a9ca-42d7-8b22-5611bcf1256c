namespace TravelTourism.Application.DTOs.Admin
{
    public class BookingReportRequest
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string ReportType { get; set; } = "summary"; // "summary", "detailed", "financial"
        public string Format { get; set; } = "pdf"; // "pdf", "excel", "csv"
        public int? TripId { get; set; }
        public string? Status { get; set; }
    }
} 