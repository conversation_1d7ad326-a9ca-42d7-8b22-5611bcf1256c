using Microsoft.EntityFrameworkCore;
using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Repositories
{
    public class TripRepository : GenericRepository<Trip>, ITripRepository
    {
        public TripRepository(ApplicationDbContext context) : base(context)
        {
        }

        public IQueryable<Trip> GetQueryable()
        {
            return _context.Trips.AsQueryable();
        }

        public async Task<IReadOnlyList<Trip>> GetFeaturedTripsAsync(int count = 10)
        {
            return await _context.Trips
                .Where(t => t.IsFeatured && t.IsActive && !t.IsDeleted)
                .Include(t => t.Category)
                .Include(t => t.DestinationCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.DepartureCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.Images)
                .OrderBy(t => t.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Trip>> GetTripsByCategoryAsync(int categoryId)
        {
            return await _context.Trips
                .Where(t => t.CategoryId == categoryId && t.IsActive && !t.IsDeleted)
                .Include(t => t.Category)
                .Include(t => t.DestinationCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.DepartureCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.Images)
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Trip>> GetTripsByDestinationAsync(int cityId)
        {
            return await _context.Trips
                .Where(t => t.DestinationCityId == cityId && t.IsActive && !t.IsDeleted)
                .Include(t => t.Category)
                .Include(t => t.DestinationCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.DepartureCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.Images)
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<Trip> GetTripWithDetailsAsync(int id)
        {
            return await _context.Trips
                .Where(t => t.Id == id && !t.IsDeleted)
                .Include(t => t.Category)
                .Include(t => t.DestinationCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.DepartureCity)
                    .ThenInclude(c => c.Country)
                .Include(t => t.Images.OrderBy(i => i.DisplayOrder))
                .Include(t => t.Itineraries.OrderBy(i => i.Day))
                .FirstOrDefaultAsync();
        }

        public async Task<IReadOnlyList<TripCategory>> GetCategoriesAsync()
        {
            return await _context.TripCategories
                .Where(c => c.IsActive && !c.IsDeleted)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<bool> IsTripAvailableAsync(int tripId, int numberOfPeople)
        {
            var trip = await _context.Trips
                .FirstOrDefaultAsync(t => t.Id == tripId && t.IsActive && !t.IsDeleted);

            if (trip == null)
                return false;

            // Check capacity
            if (numberOfPeople > trip.MaxCapacity)
                return false;

            // Check if trip is available in date range
            var today = DateTime.Today;
            return today >= trip.AvailableFrom.Date && today <= trip.AvailableTo.Date;
        }
    }
}
