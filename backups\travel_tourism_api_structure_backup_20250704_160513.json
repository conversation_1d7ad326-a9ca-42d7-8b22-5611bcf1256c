{
  "solution_name": "TravelTourismAPI",
  "description": "Clean Architecture solution for Travel & Tourism web application with Admin control over blogs/trips and User authentication with email verification",
  "projects": {
    "TravelTourism.Core": {
      "type": "Class Library",
      "target_framework": "net8.0",
      "dependencies": [],
      "structure": {
        "Entities": {
          "Base": ["BaseEntity.cs", "IAuditableEntity.cs"],
          "User": ["User.cs", "UserRole.cs", "UserToken.cs"],
          "Trip": ["Trip.cs", "TripCategory.cs", "TripImage.cs", "TripItinerary.cs"],
          "Blog": ["Blog.cs", "BlogCategory.cs", "BlogImage.cs", "BlogTag.cs"],
          "Booking": ["Booking.cs", "BookingStatus.cs", "BookingPayment.cs"],
          "Common": ["Country.cs", "City.cs", "Currency.cs"]
        },
        "ValueObjects": [
          "Email.cs",
          "Money.cs",
          "Address.cs",
          "PhoneNumber.cs",
          "DateRange.cs"
        ],
        "Interfaces": {
          "Repositories": [
            "IGenericRepository.cs",
            "IUserRepository.cs",
            "ITripRepository.cs",
            "IBlogRepository.cs",
            "IBookingRepository.cs",
            "ICountryRepository.cs",
            "ICityRepository.cs"
          ],
          "Services": [
            "IEmailService.cs",
            "IFileStorageService.cs",
            "IPaymentService.cs",
            "ICacheService.cs"
          ],
          "IUnitOfWork.cs",
          "ISpecification.cs"
        },
        "Specifications": {
          "Base": ["BaseSpecification.cs", "ISpecification.cs"],
          "UserSpecifications": [
            "UserByEmailSpecification.cs",
            "UserByRoleSpecification.cs",
            "ActiveUsersSpecification.cs",
            "UsersByCountrySpecification.cs"
          ],
          "TripSpecifications": [
            "TripsByCategorySpecification.cs",
            "TripsByDestinationSpecification.cs",
            "TripsByPriceRangeSpecification.cs",
            "AvailableTripsSpecification.cs",
            "FeaturedTripsSpecification.cs",
            "TripsByDateRangeSpecification.cs"
          ],
          "BlogSpecifications": [
            "BlogsByCategorySpecification.cs",
            "PublishedBlogsSpecification.cs",
            "BlogsByAuthorSpecification.cs",
            "BlogsByTagSpecification.cs",
            "FeaturedBlogsSpecification.cs"
          ],
          "BookingSpecifications": [
            "BookingsByUserSpecification.cs",
            "BookingsByTripSpecification.cs",
            "BookingsByStatusSpecification.cs",
            "BookingsByDateRangeSpecification.cs"
          ]
        },
        "Enums": [
          "UserRole.cs",
          "TripStatus.cs",
          "BookingStatus.cs",
          "PaymentStatus.cs",
          "BlogStatus.cs",
          "Gender.cs",
          "TripDifficulty.cs"
        ],
        "Exceptions": [
          "DomainException.cs",
          "NotFoundException.cs",
          "ValidationException.cs",
          "UnauthorizedException.cs",
          "BusinessRuleException.cs"
        ],
        "Constants": [
          "DomainConstants.cs",
          "EmailTemplates.cs",
          "FileConstants.cs"
        ]
      }
    },
    "TravelTourism.Infrastructure": {
      "type": "Class Library",
      "target_framework": "net8.0",
      "dependencies": ["TravelTourism.Core"],
      "nuget_packages": [
        "Microsoft.EntityFrameworkCore.SqlServer",
        "Microsoft.EntityFrameworkCore.Tools",
        "Microsoft.EntityFrameworkCore.Design",
        "Microsoft.Extensions.Configuration.Abstractions",
        "Microsoft.Extensions.DependencyInjection.Abstractions",
        "SendGrid",
        "CloudinaryDotNet",
        "Stripe.net",
        "StackExchange.Redis",
        "Serilog.AspNetCore",
        "Serilog.Sinks.File",
        "Serilog.Sinks.Console"
      ],
      "structure": {
        "Data": {
          "ApplicationDbContext.cs": "Main DbContext",
          "Configurations": [
            "UserConfiguration.cs",
            "TripConfiguration.cs",
            "BlogConfiguration.cs",
            "BookingConfiguration.cs",
            "CountryConfiguration.cs",
            "CityConfiguration.cs"
          ],
          "Migrations": ["Auto-generated EF migrations"],
          "Seeds": [
            "DefaultDataSeeder.cs",
            "CountryCitySeeder.cs",
            "AdminUserSeeder.cs"
          ]
        },
        "Repositories": [
          "GenericRepository.cs",
          "UserRepository.cs",
          "TripRepository.cs",
          "BlogRepository.cs",
          "BookingRepository.cs",
          "CountryRepository.cs",
          "CityRepository.cs",
          "UnitOfWork.cs"
        ],
        "Services": [
          "EmailService.cs",
          "FileStorageService.cs",
          "PaymentService.cs",
          "CacheService.cs",
          "CloudinaryService.cs",
          "StripePaymentService.cs"
        ],
        "Specifications": ["SpecificationEvaluator.cs"],
        "Extensions": [
          "QueryableExtensions.cs",
          "ServiceCollectionExtensions.cs"
        ],
        "DependencyInjection.cs": "Infrastructure services registration"
      }
    },
    "TravelTourism.Application": {
      "type": "Class Library",
      "target_framework": "net8.0",
      "dependencies": ["TravelTourism.Core"],
      "nuget_packages": [
        "AutoMapper",
        "AutoMapper.Extensions.Microsoft.DependencyInjection",
        "FluentValidation",
        "FluentValidation.DependencyInjectionExtensions",
        "Microsoft.Extensions.DependencyInjection.Abstractions",
        "Microsoft.Extensions.Logging.Abstractions",
        "System.IdentityModel.Tokens.Jwt"
      ],
      "structure": {
        "DTOs": {
          "Common": [
            "PagedResult.cs",
            "PaginationParameters.cs",
            "ApiResponse.cs",
            "FileUploadDto.cs"
          ],
          "Auth": [
            "LoginDto.cs",
            "RegisterDto.cs",
            "ForgotPasswordDto.cs",
            "ResetPasswordDto.cs",
            "VerifyEmailDto.cs",
            "TokenDto.cs",
            "AuthResultDto.cs"
          ],
          "User": [
            "UserDto.cs",
            "UserProfileDto.cs",
            "UpdateUserDto.cs",
            "UserFilterDto.cs",
            "AdminUserDto.cs"
          ],
          "Trip": [
            "TripDto.cs",
            "TripDetailDto.cs",
            "CreateTripDto.cs",
            "UpdateTripDto.cs",
            "TripFilterDto.cs",
            "TripImageDto.cs",
            "TripItineraryDto.cs",
            "TripCategoryDto.cs"
          ],
          "Blog": [
            "BlogDto.cs",
            "BlogDetailDto.cs",
            "CreateBlogDto.cs",
            "UpdateBlogDto.cs",
            "BlogFilterDto.cs",
            "BlogImageDto.cs",
            "BlogCategoryDto.cs",
            "BlogTagDto.cs"
          ],
          "Booking": [
            "BookingDto.cs",
            "BookingDetailDto.cs",
            "CreateBookingDto.cs",
            "UpdateBookingDto.cs",
            "BookingFilterDto.cs",
            "BookingPaymentDto.cs"
          ],
          "Common": [
            "CountryDto.cs",
            "CityDto.cs",
            "CurrencyDto.cs"
          ]
        },
        "Services": [
          "AuthService.cs",
          "UserService.cs",
          "TripService.cs",
          "BlogService.cs",
          "BookingService.cs",
          "FileService.cs",
          "AdminService.cs"
        ],
        "Interfaces": [
          "IAuthService.cs",
          "IUserService.cs",
          "ITripService.cs",
          "IBlogService.cs",
          "IBookingService.cs",
          "IFileService.cs",
          "IAdminService.cs"
        ],
        "Mappings": [
          "MappingProfile.cs",
          "UserMappingProfile.cs",
          "TripMappingProfile.cs",
          "BlogMappingProfile.cs",
          "BookingMappingProfile.cs"
        ],
        "Validators": {
          "Auth": [
            "LoginValidator.cs",
            "RegisterValidator.cs",
            "ForgotPasswordValidator.cs",
            "ResetPasswordValidator.cs"
          ],
          "User": [
            "UpdateUserValidator.cs",
            "UserProfileValidator.cs"
          ],
          "Trip": [
            "CreateTripValidator.cs",
            "UpdateTripValidator.cs"
          ],
          "Blog": [
            "CreateBlogValidator.cs",
            "UpdateBlogValidator.cs"
          ],
          "Booking": [
            "CreateBookingValidator.cs",
            "UpdateBookingValidator.cs"
          ]
        },
        "Exceptions": [
          "ApplicationException.cs",
          "ValidationException.cs",
          "AuthenticationException.cs"
        ],
        "Common": [
          "Result.cs",
          "PagedList.cs",
          "JwtHelper.cs",
          "EmailTemplateHelper.cs"
        ],
        "DependencyInjection.cs": "Application services registration"
      }
    },
    "TravelTourism.API": {
      "type": "Web API",
      "target_framework": "net8.0",
      "dependencies": ["TravelTourism.Application", "TravelTourism.Infrastructure"],
      "nuget_packages": [
        "Microsoft.AspNetCore.Authentication.JwtBearer",
        "Microsoft.AspNetCore.Identity.EntityFrameworkCore",
        "Microsoft.AspNetCore.Mvc.Versioning",
        "Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer",
        "Swashbuckle.AspNetCore",
        "Swashbuckle.AspNetCore.Annotations",
        "Microsoft.AspNetCore.Cors",
        "Microsoft.AspNetCore.RateLimiting",
        "AspNetCoreRateLimit",
        "Serilog.AspNetCore",
        "Microsoft.Extensions.Diagnostics.HealthChecks",
        "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore",
        "FluentValidation.AspNetCore"
      ],
      "structure": {
        "Controllers": {
          "Base": ["BaseController.cs"],
          "V1": [
            "AuthController.cs",
            "UsersController.cs",
            "TripsController.cs",
            "BlogsController.cs",
            "BookingsController.cs",
            "FilesController.cs",
            "CountriesController.cs",
            "CitiesController.cs"
          ],
          "Admin": [
            "AdminController.cs",
            "AdminTripsController.cs",
            "AdminBlogsController.cs",
            "AdminUsersController.cs",
            "AdminBookingsController.cs",
            "AdminDashboardController.cs"
          ]
        },
        "Middleware": [
          "ExceptionHandlingMiddleware.cs",
          "RequestLoggingMiddleware.cs",
          "JwtMiddleware.cs",
          "RateLimitingMiddleware.cs"
        ],
        "Filters": [
          "ValidationFilter.cs",
          "AuthorizeFilter.cs",
          "CacheFilter.cs",
          "LogActionFilter.cs"
        ],
        "Extensions": [
          "ServiceCollectionExtensions.cs",
          "ApplicationBuilderExtensions.cs",
          "ControllerBaseExtensions.cs"
        ],
        "Models": {
          "Requests": [
            "PaginationRequest.cs",
            "FileUploadRequest.cs"
          ],
          "Responses": [
            "ApiResponse.cs",
            "ErrorResponse.cs",
            "ValidationErrorResponse.cs"
          ]
        },
        "Configurations": [
          "JwtConfiguration.cs",
          "EmailConfiguration.cs",
          "FileStorageConfiguration.cs",
          "PaymentConfiguration.cs",
          "RateLimitConfiguration.cs"
        ],
        "Program.cs": "Application entry point",
        "appsettings.json": "Base configuration",
        "appsettings.Development.json": "Development configuration",
        "appsettings.Production.json": "Production configuration"
      }
    }
  },
  "configuration_files": {
    "appsettings.json": {
      "ConnectionStrings": {
        "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=TravelTourismDB;Trusted_Connection=true;MultipleActiveResultSets=true"
      },
      "JwtSettings": {
        "Key": "your-super-secret-key-here-make-it-long-and-complex",
        "Issuer": "TravelTourismAPI",
        "Audience": "TravelTourismAPI",
        "DurationInMinutes": 60
      },
      "EmailSettings": {
        "ApiKey": "your-sendgrid-api-key",
        "FromEmail": "<EMAIL>",
        "FromName": "Travel & Tourism"
      },
      "FileStorage": {
        "CloudinarySettings": {
          "CloudName": "your-cloudinary-cloud-name",
          "ApiKey": "your-cloudinary-api-key",
          "ApiSecret": "your-cloudinary-api-secret"
        }
      },
      "PaymentSettings": {
        "Stripe": {
          "PublishableKey": "your-stripe-publishable-key",
          "SecretKey": "your-stripe-secret-key"
        }
      },
      "Redis": {
        "ConnectionString": "localhost:6379"
      },
      "RateLimiting": {
        "EnableRateLimiting": true,
        "Rules": [
          {
            "Endpoint": "*",
            "Period": "1m",
            "Limit": 100
          },
          {
            "Endpoint": "*/auth/*",
            "Period": "1m",
            "Limit": 10
          }
        ]
      }
    }
  },
  "database_schema": {
    "tables": [
      {
        "name": "Users",
        "description": "User accounts with authentication and profile info",
        "columns": [
          "Id (int, PK)",
          "FirstName (nvarchar(100))",
          "LastName (nvarchar(100))",
          "Email (nvarchar(256), unique)",
          "PhoneNumber (nvarchar(20))",
          "DateOfBirth (datetime2, nullable)",
          "Gender (int, enum)",
          "ProfileImageUrl (nvarchar(500), nullable)",
          "AddressLine1 (nvarchar(200), nullable)",
          "AddressLine2 (nvarchar(200), nullable)",
          "City (nvarchar(100), nullable)",
          "State (nvarchar(100), nullable)",
          "Country (nvarchar(100), nullable)",
          "PostalCode (nvarchar(20), nullable)",
          "PasswordHash (nvarchar(500))",
          "Role (int, enum: User=1, Admin=2)",
          "IsEmailVerified (bit, default: false)",
          "EmailVerificationToken (nvarchar(500), nullable)",
          "EmailVerificationTokenExpiry (datetime2, nullable)",
          "PasswordResetToken (nvarchar(500), nullable)",
          "PasswordResetTokenExpiry (datetime2, nullable)",
          "IsActive (bit, default: true)",
          "LastLoginAt (datetime2, nullable)",
          "CreatedAt (datetime2, default: GETUTCDATE())",
          "UpdatedAt (datetime2, nullable)",
          "IsDeleted (bit, default: false)"
        ]
      },
      {
        "name": "TripCategories",
        "description": "Trip categories (Adventure, Cultural, Beach, etc.)",
        "columns": [
          "Id (int, PK)",
          "Name (nvarchar(100))",
          "Description (nvarchar(500), nullable)",
          "IconUrl (nvarchar(500), nullable)",
          "IsActive (bit, default: true)",
          "CreatedAt (datetime2, default: GETUTCDATE())",
          "UpdatedAt (datetime2, nullable)"
        ]
      },
      {
        "name": "Trips",
        "description": "Travel packages and tours",
        "columns": [
          "Id (int, PK)",
          "Name (nvarchar(200))",
          "Description (nvarchar(max))",
          "ShortDescription (nvarchar(500))",
          "Price (decimal(18,2))",
          "DiscountPrice (decimal(18,2), nullable)",
          "Duration (int, in days)",
          "MaxCapacity (int)",
          "MinAge (int, nullable)",
          "MaxAge (int, nullable)",
          "Difficulty (int, enum: Easy=1, Moderate=2, Hard=3)",
          "CategoryId (int, FK to TripCategories)",
          "DestinationCityId (int, FK to Cities)",
          "DepartureCityId (int, FK to Cities)",
          "IncludesAccommodation (bit, default: false)",
          "IncludesTransport (bit, default: false)",
          "IncludesMeals (bit, default: false)",
          "IncludesGuide (bit, default: false)",
          "MainImageUrl (nvarchar(500))",
          "IsActive (bit, default: true)",
          "IsFeatured (bit, default: false)",
          "AvailableFrom (datetime2)",
          "AvailableTo (datetime2)",
          "CreatedAt (datetime2, default: GETUTCDATE())",
          "UpdatedAt (datetime2, nullable)",
          "IsDeleted (bit, default: false)"
        ]
      },
      {
        "name": "TripImages",
        "description": "Multiple images for each trip",
        "columns": [
          "Id (int, PK)",
          "TripId (int, FK to Trips)",
          "ImageUrl (nvarchar(500))",
          "Caption (nvarchar(200), nullable)",
          "IsMain (bit, default: false)",
          "DisplayOrder (int, default: 0)",
          "CreatedAt (datetime2, default: GETUTCDATE())"
        ]
      },
      {
        "name": "TripItineraries",
        "description": "Day-by-day itinerary for trips",
        "columns": [
          "Id (int, PK)",
          "TripId (int, FK to Trips)",
          "Day (int)",
          "Title (nvarchar(200))",
          "Description (nvarchar(1000))",
          "Activities (nvarchar(1000), nullable)",
          "Meals (nvarchar(200), nullable)",
          "Accommodation (nvarchar(200), nullable)"
        ]
      },
      {
        "name": "BlogCategories",
        "description": "Blog categories (Travel Tips, Destinations, etc.)",
        "columns": [
          "Id (int, PK)",
          "Name (nvarchar(100))",
          "Description (nvarchar(500), nullable)",
          "IsActive (bit, default: true)",
          "CreatedAt (datetime2, default: GETUTCDATE())",
          "UpdatedAt (datetime2, nullable)"
        ]
      },
      {
        "name": "BlogTags",
        "description": "Tags for blog posts",
        "columns": [
          "Id (int, PK)",
          "Name (nvarchar(50))",
          "CreatedAt (datetime2, default: GETUTCDATE())"
        ]
      },
      {
        "name": "Blogs",
        "description": "Travel blogs and articles",
        "columns": [
          "Id (int, PK)",
          "Title (nvarchar(200))",
          "Slug (nvarchar(200), unique)",
          "Content (nvarchar(max))",
          "Excerpt (nvarchar(500))",
          "AuthorId (int, FK to Users)",
          "CategoryId (int, FK to BlogCategories)",
          "FeaturedImageUrl (nvarchar(500), nullable)",
          "IsPublished (bit, default: false)",
          "IsFeatured (bit, default: false)",
          "PublishedAt (datetime2, nullable)",
          "ViewCount (int, default: 0)",
          "ReadingTime (int, in minutes)",
          "CreatedAt (datetime2, default: GETUTCDATE())",
          "UpdatedAt (datetime2, nullable)",
          "IsDeleted (bit, default: false)"
        ]
      },
      {
        "name": "BlogImages",
        "description": "Additional images for blog posts",
        "columns": [
          "Id (int, PK)",
          "BlogId (int, FK to Blogs)",
          "ImageUrl (nvarchar(500))",
          "Caption (nvarchar(200), nullable)",
          "DisplayOrder (int, default: 0)",
          "CreatedAt (datetime2, default: GETUTCDATE())"
        ]
      },
      {
        "name": "BlogTagMappings",
        "description": "Many-to-many relationship between blogs and tags",
        "columns": [
          "BlogId (int, FK to Blogs)",
          "TagId (int, FK to BlogTags)",
          "Primary Key: (BlogId, TagId)"
        ]
      },
      {
        "name": "Bookings",
        "description": "Trip bookings by users",
        "columns": [
          "Id (int, PK)",
          "BookingNumber (nvarchar(50), unique)",
          "UserId (int, FK to Users)",
          "TripId (int, FK to Trips)",
          "BookingDate (datetime2, default: GETUTCDATE())",
          "TravelDate (datetime2)",
          "NumberOfAdults (int, default: 1)",
          "NumberOfChildren (int, default: 0)",
          "NumberOfInfants (int, default: 0)",
          "TotalPeople (int, computed: NumberOfAdults + NumberOfChildren + NumberOfInfants)",
          "PricePerPerson (decimal(18,2))",
          "TotalAmount (decimal(18,2))",
          "DiscountAmount (decimal(18,2), default: 0)",
          "FinalAmount (decimal(18,2))",
          "Status (int, enum: Pending=1, Confirmed=2, Cancelled=3, Completed=4)",
          "PaymentStatus (int, enum: Pending=1, Paid=2, Failed=3, Refunded=4)",
          "SpecialRequests (nvarchar(1000), nullable)",
          "EmergencyContactName (nvarchar(100), nullable)",
          "EmergencyContactPhone (nvarchar(20), nullable)",
          "CancellationReason (nvarchar(500), nullable)",
          "CancelledAt (datetime2, nullable)",
          "CreatedAt (datetime2, default: GETUTCDATE())",
          "UpdatedAt (datetime2, nullable)",
          "IsDeleted (bit, default: false)"
        ]
      },
      {
        "name": "BookingPayments",
        "description": "Payment records for bookings",
        "columns": [
          "Id (int, PK)",
          "BookingId (int, FK to Bookings)",
          "PaymentMethod (nvarchar(50), e.g., 'Stripe', 'PayPal')",
          "TransactionId (nvarchar(200))",
          "Amount (decimal(18,2))",
          "Currency (nvarchar(10), default: 'USD')",
          "Status (int, enum: Pending=1, Success=2, Failed=3, Refunded=4)",
          "PaymentDate (datetime2, nullable)",
          "FailureReason (nvarchar(500), nullable)",
          "RefundAmount (decimal(18,2), nullable)",
          "RefundDate (datetime2, nullable)",
          "CreatedAt (datetime2, default: GETUTCDATE())",
          "UpdatedAt (datetime2, nullable)"
        ]
      },
      {
        "name": "Countries",
        "description": "Country master data",
        "columns": [
          "Id (int, PK)",
          "Name (nvarchar(100))",
          "Code (nvarchar(5), ISO country code)",
          "Currency (nvarchar(50))",
          "CurrencyCode (nvarchar(10))",
          "CurrencySymbol (nvarchar(5))",
          "FlagUrl (nvarchar(500), nullable)",
          "IsActive (bit, default: true)",
          "CreatedAt (datetime2, default: GETUTCDATE())"
        ]
      },
      {
        "name": "Cities",
        "description": "City master data",
        "columns": [
          "Id (int, PK)",
          "Name (nvarchar(100))",
          "CountryId (int, FK to Countries)",
          "StateProvince (nvarchar(100), nullable)",
          "Latitude (decimal(10,8), nullable)",
          "Longitude (decimal(11,8), nullable)",
          "TimeZone (nvarchar(50), nullable)",
          "IsPopular (bit, default: false)",
          "IsActive (bit, default: true)",
          "CreatedAt (datetime2, default: GETUTCDATE())"
        ]
      },
      {
        "name": "UserTokens",
        "description": "JWT refresh tokens for users",
        "columns": [
          "Id (int, PK)",
          "UserId (int, FK to Users)",
          "RefreshToken (nvarchar(500))",
          "ExpiryDate (datetime2)",
          "IsActive (bit, default: true)",
          "CreatedAt (datetime2, default: GETUTCDATE())"
        ]
      }
    ]
  },
  "authentication_flow": {
    "register": [
      "User submits registration form",
      "System validates data",
      "System creates user account (IsEmailVerified = false)",
      "System generates email verification token",
      "System sends verification email via SendGrid",
      "User clicks verification link",
      "System verifies token and activates account"
    ],
    "login": [
      "User submits credentials",
      "System validates email/password",
      "System checks if email is verified",
      "System generates JWT token",
      "System returns token and user info"
    ],
    "forgot_password": [
      "User submits email",
      "System validates email exists",
      "System generates reset token",
      "System sends reset email",
      "User clicks reset link",
      "User enters new password",
      "System validates token and updates password"
    ]
  },
  "api_endpoints": {
    "auth": [
      "POST /api/v1/auth/register",
      "POST /api/v1/auth/login",
      "POST /api/v1/auth/verify-email",
      "POST /api/v1/auth/forgot-password",
      "POST /api/v1/auth/reset-password",
      "POST /api/v1/auth/refresh-token"
    ],
    "users": [
      "GET /api/v1/users/profile",
      "PUT /api/v1/users/profile",
      "GET /api/v1/users/bookings",
      "DELETE /api/v1/users/account"
    ],
    "trips": [
      "GET /api/v1/trips",
      "GET /api/v1/trips/{id}",
      "GET /api/v1/trips/categories",
      "GET /api/v1/trips/destinations",
      "GET /api/v1/trips/featured"
    ],
    "blogs": [
      "GET /api/v1/blogs",
      "GET /api/v1/blogs/{id}",
      "GET /api/v1/blogs/categories",
      "GET /api/v1/blogs/featured"
    ],
    "bookings": [
      "POST /api/v1/bookings",
      "GET /api/v1/bookings/{id}",
      "PUT /api/v1/bookings/{id}/cancel",
      "POST /api/v1/bookings/{id}/payment"
    ],
    "admin": [
      "GET /api/v1/admin/dashboard",
      "GET /api/v1/admin/users",
      "GET /api/v1/admin/trips",
      "POST /api/v1/admin/trips",
      "PUT /api/v1/admin/trips/{id}",
      "DELETE /api/v1/admin/trips/{id}",
      "GET /api/v1/admin/blogs",
      "POST /api/v1/admin/blogs",
      "PUT /api/v1/admin/blogs/{id}",
      "DELETE /api/v1/admin/blogs/{id}",
      "GET /api/v1/admin/bookings"
    ]
  },
  "security_features": [
    "JWT Authentication",
    "Email Verification",
    "Password Reset",
    "Rate Limiting",
    "CORS Policy",
    "Input Validation",
    "SQL Injection Protection",
    "XSS Protection",
    "Role-based Authorization"
  ]
}