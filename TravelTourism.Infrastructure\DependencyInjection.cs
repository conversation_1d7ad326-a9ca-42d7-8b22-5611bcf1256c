using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SendGrid.Extensions.DependencyInjection;
using StackExchange.Redis;
using TravelTourism.Core.Interfaces;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.Core.Interfaces.Services;
using TravelTourism.Infrastructure.Data;
using TravelTourism.Infrastructure.Repositories;
using TravelTourism.Infrastructure.Services;

namespace TravelTourism.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Database
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));

            // Unit of Work & Repositories
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<ITripRepository, TripRepository>();
            services.AddScoped<IBlogRepository, BlogRepository>();
            services.AddScoped<IBookingRepository, BookingRepository>();
            services.AddScoped<ICountryRepository, CountryRepository>();
            services.AddScoped<ICityRepository, CityRepository>();

            // Redis Cache
            var redisConnectionString = configuration["Redis:ConnectionString"];
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                services.AddSingleton<IConnectionMultiplexer>(sp =>
                {
                    return ConnectionMultiplexer.Connect(redisConnectionString);
                });
                services.AddScoped<ICacheService, CacheService>();
            }
            else
            {
                // Use in-memory cache as fallback when Redis is not available
                services.AddMemoryCache();
                services.AddScoped<ICacheService, InMemoryCacheService>();
            }

            // HttpClient for external services
            services.AddHttpClient();

            // External Services
            services.AddSendGrid(options =>
            {
                options.ApiKey = configuration["EmailSettings:ApiKey"];
            });

            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IFileStorageService, FileStorageService>();
            services.AddScoped<IPaymentService, PaymentService>();

            return services;
        }
    }
}
