using Microsoft.EntityFrameworkCore;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Data.Seeds;

public class AdminUserSeeder
{
    private readonly ApplicationDbContext _context;

    public AdminUserSeeder(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task SeedAsync()
    {
        // Seed admin user here
        await _context.SaveChangesAsync();
    }
} 