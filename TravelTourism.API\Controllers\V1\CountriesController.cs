using Microsoft.AspNetCore.Mvc;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.API.Controllers.Base;

namespace TravelTourism.API.Controllers.V1;

[ApiController]
[Route("api/v1/[controller]")]
public class CountriesController : BaseController
{
    private readonly ICountryRepository _countryRepository;

    public CountriesController(ICountryRepository countryRepository)
    {
        _countryRepository = countryRepository;
    }

    [HttpGet]
    public async Task<IActionResult> GetCountries()
    {
        var countries = await _countryRepository.GetAllAsync();
        return Ok(countries);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetCountry(int id)
    {
        var country = await _countryRepository.GetByIdAsync(id);
        if (country == null)
            return NotFound();
        
        return Ok(country);
    }
} 