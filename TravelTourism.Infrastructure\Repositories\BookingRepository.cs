using Microsoft.EntityFrameworkCore;
using TravelTourism.Core.Entities.Booking;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Repositories
{
    public class BookingRepository : GenericRepository<Booking>, IBookingRepository
    {
        public BookingRepository(ApplicationDbContext context) : base(context)
        {
        }

        public IQueryable<Booking> GetQueryable()
        {
            return _context.Bookings.AsQueryable();
        }

        public async Task<IReadOnlyList<Booking>> GetUserBookingsAsync(int userId)
        {
            return await _context.Bookings
                .Where(b => b.UserId == userId && !b.IsDeleted)
                .Include(b => b.Trip)
                    .ThenInclude(t => t.Category)
                .Include(b => b.Trip)
                    .ThenInclude(t => t.DestinationCity)
                        .ThenInclude(c => c.Country)
                .Include(b => b.Payments)
                .OrderByDescending(b => b.BookingDate)
                .ToListAsync();
        }

        public async Task<Booking> GetByBookingNumberAsync(string bookingNumber)
        {
            return await _context.Bookings
                .Where(b => b.BookingNumber == bookingNumber && !b.IsDeleted)
                .Include(b => b.User)
                .Include(b => b.Trip)
                    .ThenInclude(t => t.Category)
                .Include(b => b.Trip)
                    .ThenInclude(t => t.DestinationCity)
                        .ThenInclude(c => c.Country)
                .Include(b => b.Payments)
                .FirstOrDefaultAsync();
        }

        public async Task<IReadOnlyList<Booking>> GetTripBookingsAsync(int tripId)
        {
            return await _context.Bookings
                .Where(b => b.TripId == tripId && !b.IsDeleted)
                .Include(b => b.User)
                .Include(b => b.Payments)
                .OrderByDescending(b => b.BookingDate)
                .ToListAsync();
        }

        public async Task<Booking> GetBookingWithDetailsAsync(int id)
        {
            return await _context.Bookings
                .Where(b => b.Id == id && !b.IsDeleted)
                .Include(b => b.User)
                .Include(b => b.Trip)
                    .ThenInclude(t => t.Category)
                .Include(b => b.Trip)
                    .ThenInclude(t => t.DestinationCity)
                        .ThenInclude(c => c.Country)
                .Include(b => b.Trip)
                    .ThenInclude(t => t.DepartureCity)
                        .ThenInclude(c => c.Country)
                .Include(b => b.Payments)
                .FirstOrDefaultAsync();
        }

        public async Task<int> GetTripBookedCapacityAsync(int tripId, DateTime travelDate)
        {
            return await _context.Bookings
                .Where(b => b.TripId == tripId 
                    && b.TravelDate.Date == travelDate.Date 
                    && (b.Status == Core.Enums.BookingStatus.Confirmed || b.Status == Core.Enums.BookingStatus.Pending)
                    && !b.IsDeleted)
                .SumAsync(b => b.TotalPeople);
        }

        public async Task<bool> IsBookingNumberUniqueAsync(string bookingNumber)
        {
            return !await _context.Bookings
                .AnyAsync(b => b.BookingNumber == bookingNumber && !b.IsDeleted);
        }
    }
}
