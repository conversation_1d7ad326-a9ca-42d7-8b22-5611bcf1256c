namespace TravelTourism.Application.DTOs.Admin
{
    public class ExportBlogsRequest
    {
        public string Format { get; set; } = "csv"; // "csv", "excel", "pdf"
        public string? SearchTerm { get; set; }
        public int? CategoryId { get; set; }
        public string? Author { get; set; }
        public bool? IsPublished { get; set; }
        public DateTime? PublishedDateFrom { get; set; }
        public DateTime? PublishedDateTo { get; set; }
        public List<string> Fields { get; set; } = new();
    }
} 