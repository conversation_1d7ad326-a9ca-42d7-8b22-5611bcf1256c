using Microsoft.AspNetCore.Mvc;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.Models.Requests;

namespace TravelTourism.API.Controllers.V1;

[ApiController]
[Route("api/v1/[controller]")]
public class FilesController : BaseController
{
    private readonly IFileService _fileService;

    public FilesController(IFileService fileService)
    {
        _fileService = fileService;
    }

    [HttpPost("upload-image")]
    public async Task<IActionResult> UploadImage([FromForm] IFormFile file, [FromForm] string? folder = null)
    {
        var request = new FileUploadRequest { File = file, Folder = folder };
        var result = await _fileService.UploadImageAsync(request);

        if (!result.Success)
        {
            return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
        }

        return Ok(CreateSuccessResponse(new { ImageUrl = result.Data }, "Image uploaded successfully"));
    }

    [HttpPost("upload-document")]
    public async Task<IActionResult> UploadDocument([FromForm] IFormFile file, [FromForm] string? folder = null)
    {
        var request = new FileUploadRequest { File = file, Folder = folder };
        var result = await _fileService.UploadDocumentAsync(request);

        if (!result.Success)
        {
            return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
        }

        return Ok(CreateSuccessResponse(new { DocumentUrl = result.Data }, "Document uploaded successfully"));
    }
} 