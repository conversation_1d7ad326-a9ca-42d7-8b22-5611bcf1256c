using System.Collections.Generic;
using System.Threading.Tasks;
using TravelTourism.Core.Entities.Common;

namespace TravelTourism.Core.Interfaces.Repositories
{
    public interface ICityRepository : IGenericRepository<City>
    {
        Task<IReadOnlyList<City>> GetCitiesByCountryAsync(int countryId);
        Task<IReadOnlyList<City>> GetPopularCitiesAsync();
        Task<IReadOnlyList<City>> GetActiveCitiesAsync();
        Task<City> GetCityWithCountryAsync(int id);
    }
}
