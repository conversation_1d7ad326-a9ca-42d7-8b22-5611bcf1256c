namespace TravelTourism.Application.Common
{
    public class Result
    {
        public bool IsSuccess { get; }
        public bool IsFailure => !IsSuccess;
        public string? Error { get; }
        public List<string> Errors { get; }

        protected Result(bool isSuccess, string? error, List<string>? errors = null)
        {
            IsSuccess = isSuccess;
            Error = error;
            Errors = errors ?? new List<string>();
        }

        public static Result Success() => new Result(true, null);
        public static Result Failure(string error) => new Result(false, error);
        public static Result Failure(List<string> errors) => new Result(false, null, errors);

        public static Result<T> Success<T>(T value) => new Result<T>(value, true, null);
        public static Result<T> Failure<T>(string error) => new Result<T>(default(T), false, error);
        public static Result<T> Failure<T>(List<string> errors) => new Result<T>(default(T), false, null, errors);
    }

    public class Result<T> : Result
    {
        public T? Value { get; }

        protected internal Result(T? value, bool isSuccess, string? error, List<string>? errors = null) 
            : base(isSuccess, error, errors)
        {
            Value = value;
        }
    }
}
