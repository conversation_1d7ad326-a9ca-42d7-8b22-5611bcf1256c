{"format": 1, "restore": {"F:\\torism and travel\\TravelTourism.Infrastructure\\TravelTourism.Infrastructure.csproj": {}}, "projects": {"F:\\torism and travel\\TravelTourism.Core\\TravelTourism.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\torism and travel\\TravelTourism.Core\\TravelTourism.Core.csproj", "projectName": "TravelTourism.Core", "projectPath": "F:\\torism and travel\\TravelTourism.Core\\TravelTourism.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\torism and travel\\TravelTourism.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 22.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "F:\\torism and travel\\TravelTourism.Infrastructure\\TravelTourism.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\torism and travel\\TravelTourism.Infrastructure\\TravelTourism.Infrastructure.csproj", "projectName": "TravelTourism.Infrastructure", "projectPath": "F:\\torism and travel\\TravelTourism.Infrastructure\\TravelTourism.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\torism and travel\\TravelTourism.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 22.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 22.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\torism and travel\\TravelTourism.Core\\TravelTourism.Core.csproj": {"projectPath": "F:\\torism and travel\\TravelTourism.Core\\TravelTourism.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CloudinaryDotNet": {"target": "Package", "version": "[1.27.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "SendGrid": {"target": "Package", "version": "[9.29.3, )"}, "SendGrid.Extensions.DependencyInjection": {"target": "Package", "version": "[1.0.1, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.7.10, )"}, "Stripe.net": {"target": "Package", "version": "[43.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}