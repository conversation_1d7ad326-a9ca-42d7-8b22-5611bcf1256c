using TravelTourism.Core.Entities.Booking;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BookingSpecifications;

public class BookingsByUserSpecification : BaseSpecification<Booking>
{
    public BookingsByUserSpecification(int userId)
        : base(b => b.UserId == userId && !b.<PERSON>)
    {
        AddInclude(b => b.Trip);
        AddInclude(b => b.User);
        AddInclude(b => b.Payments);
        AddOrderByDescending(b => b.CreatedAt);
    }
} 