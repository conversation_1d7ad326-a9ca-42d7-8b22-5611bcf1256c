using System;
using System.Text.RegularExpressions;

namespace TravelTourism.Core.ValueObjects
{
    public class PhoneNumber
    {
        private static readonly Regex PhoneRegex = new Regex(
            @"^\+?[1-9]\d{1,14}$",
            RegexOptions.Compiled);

        public string Value { get; private set; }

        public PhoneNumber(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Phone number cannot be empty", nameof(value));

            // Remove spaces, dashes, and parentheses
            var cleanValue = value.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            if (!PhoneRegex.IsMatch(cleanValue))
                throw new ArgumentException("Invalid phone number format", nameof(value));

            Value = cleanValue;
        }

        public static implicit operator string(PhoneNumber phoneNumber) => phoneNumber.Value;
        public static implicit operator PhoneNumber(string value) => new PhoneNumber(value);

        public override string ToString() => Value;
        public override bool Equals(object obj) => obj is PhoneNumber phone && Value == phone.Value;
        public override int GetHashCode() => Value.GetHashCode();
    }
}
