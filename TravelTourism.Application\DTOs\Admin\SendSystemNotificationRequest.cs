namespace TravelTourism.Application.DTOs.Admin
{
    public class SendSystemNotificationRequest
    {
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Type { get; set; } = "info"; // "info", "warning", "error", "success"
        public string? TargetAudience { get; set; } // "all", "admins", "users", "specific"
        public List<int>? UserIds { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }
} 