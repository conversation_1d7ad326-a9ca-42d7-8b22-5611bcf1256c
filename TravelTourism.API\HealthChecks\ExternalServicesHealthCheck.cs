using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http;

namespace TravelTourism.API.HealthChecks;

public class ExternalServicesHealthCheck : IHealthCheck
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ExternalServicesHealthCheck> _logger;
    private readonly HttpClient _httpClient;

    public ExternalServicesHealthCheck(
        IConfiguration configuration,
        ILogger<ExternalServicesHealthCheck> logger,
        HttpClient httpClient)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var healthChecks = new List<(string Service, Task<bool> Check)>();

        // Check SendGrid
        healthChecks.Add(("SendGrid", CheckSendGridHealthAsync()));

        // Check Cloudinary
        healthChecks.Add(("Cloudinary", CheckCloudinaryHealthAsync()));

        // Check Redis (if configured)
        var redisConnectionString = _configuration["Redis:ConnectionString"];
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            healthChecks.Add(("Redis", CheckRedisHealthAsync()));
        }

        // Wait for all checks to complete
        await Task.WhenAll(healthChecks.Select(hc => hc.Check));

        var failedServices = new List<string>();
        var healthyServices = new List<string>();

        foreach (var (service, checkTask) in healthChecks)
        {
            if (await checkTask)
            {
                healthyServices.Add(service);
            }
            else
            {
                failedServices.Add(service);
            }
        }

        if (failedServices.Any())
        {
            _logger.LogWarning("External services health check failed for: {FailedServices}", string.Join(", ", failedServices));
            return HealthCheckResult.Unhealthy(
                description: $"External services unhealthy: {string.Join(", ", failedServices)}",
                data: new Dictionary<string, object>
                {
                    ["HealthyServices"] = healthyServices,
                    ["FailedServices"] = failedServices
                });
        }

        _logger.LogInformation("All external services are healthy: {HealthyServices}", string.Join(", ", healthyServices));
        return HealthCheckResult.Healthy(
            description: "All external services are healthy",
            data: new Dictionary<string, object>
            {
                ["HealthyServices"] = healthyServices
            });
    }

    private async Task<bool> CheckSendGridHealthAsync()
    {
        try
        {
            var apiKey = _configuration["EmailSettings:ApiKey"];
            if (string.IsNullOrEmpty(apiKey))
            {
                return false;
            }

            // SendGrid doesn't have a public health endpoint, so we'll just check if the API key is configured
            return !string.IsNullOrEmpty(apiKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "SendGrid health check failed");
            return false;
        }
    }

    private async Task<bool> CheckCloudinaryHealthAsync()
    {
        try
        {
            var cloudName = _configuration["FileStorage:CloudinarySettings:CloudName"];
            if (string.IsNullOrEmpty(cloudName))
            {
                return false;
            }

            // Check Cloudinary's status page or ping their API
            var response = await _httpClient.GetAsync($"https://{cloudName}.cloudinary.com/api/v1_1/{cloudName}/ping", cancellationToken: CancellationToken.None);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Cloudinary health check failed");
            return false;
        }
    }

    private async Task<bool> CheckRedisHealthAsync()
    {
        try
        {
            var redisConnectionString = _configuration["Redis:ConnectionString"];
            if (string.IsNullOrEmpty(redisConnectionString))
            {
                return false;
            }

            // For Redis, we'll just check if the connection string is configured
            // In a real implementation, you might want to actually connect to Redis
            return !string.IsNullOrEmpty(redisConnectionString);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis health check failed");
            return false;
        }
    }
} 