using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Infrastructure.Services
{
    public class EmailService : IEmailService
    {
        private readonly ISendGridClient _sendGridClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;
        private readonly string _fromEmail;
        private readonly string _fromName;

        public EmailService(
            ISendGridClient sendGridClient,
            IConfiguration configuration,
            ILogger<EmailService> logger)
        {
            _sendGridClient = sendGridClient;
            _configuration = configuration;
            _logger = logger;
            _fromEmail = configuration["EmailSettings:FromEmail"];
            _fromName = configuration["EmailSettings:FromName"];
        }

        public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            try
            {
                var from = new EmailAddress(_fromEmail, _fromName);
                var toEmail = new EmailAddress(to);
                
                var msg = MailHelper.CreateSingleEmail(from, toEmail, subject, isHtml ? null : body, isHtml ? body : null);
                
                var response = await _sendGridClient.SendEmailAsync(msg);
                
                if (response.StatusCode != System.Net.HttpStatusCode.Accepted)
                {
                    var responseBody = await response.Body.ReadAsStringAsync();
                    _logger.LogError($"Failed to send email to {to}. Status: {response.StatusCode}, Body: {responseBody}");
                }
                else
                {
                    _logger.LogInformation($"Email sent successfully to {to}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending email to {to}");
                throw;
            }
        }

        public async Task SendEmailVerificationAsync(string to, string verificationLink)
        {
            var subject = "Verify Your Email - Travel & Tourism";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5aa0;'>Welcome to Travel & Tourism!</h2>
                        <p>Thank you for registering with us. To complete your registration, please verify your email address by clicking the button below:</p>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{verificationLink}' 
                               style='background-color: #2c5aa0; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                                Verify Email Address
                            </a>
                        </div>
                        
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style='word-break: break-all; color: #666;'>{verificationLink}</p>
                        
                        <p style='margin-top: 30px; font-size: 14px; color: #666;'>
                            This verification link will expire in 24 hours. If you didn't create an account with us, please ignore this email.
                        </p>
                        
                        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                        <p style='font-size: 12px; color: #999; text-align: center;'>
                            Travel & Tourism - Your Adventure Starts Here
                        </p>
                    </div>
                </body>
                </html>";

            await SendEmailAsync(to, subject, body);
        }

        public async Task SendPasswordResetAsync(string to, string resetLink)
        {
            var subject = "Password Reset - Travel & Tourism";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5aa0;'>Password Reset Request</h2>
                        <p>We received a request to reset your password. Click the button below to reset your password:</p>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{resetLink}' 
                               style='background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                                Reset Password
                            </a>
                        </div>
                        
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style='word-break: break-all; color: #666;'>{resetLink}</p>
                        
                        <p style='margin-top: 30px; font-size: 14px; color: #666;'>
                            This password reset link will expire in 1 hour. If you didn't request a password reset, please ignore this email.
                        </p>
                        
                        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                        <p style='font-size: 12px; color: #999; text-align: center;'>
                            Travel & Tourism - Your Adventure Starts Here
                        </p>
                    </div>
                </body>
                </html>";

            await SendEmailAsync(to, subject, body);
        }

        public async Task SendBookingConfirmationAsync(string to, string bookingNumber, string tripName)
        {
            var subject = $"Booking Confirmation - {bookingNumber}";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #28a745;'>Booking Confirmed!</h2>
                        <p>Great news! Your booking has been confirmed.</p>
                        
                        <div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                            <h3 style='margin-top: 0; color: #2c5aa0;'>Booking Details</h3>
                            <p><strong>Booking Number:</strong> {bookingNumber}</p>
                            <p><strong>Trip:</strong> {tripName}</p>
                        </div>
                        
                        <p>You will receive a detailed itinerary and travel information closer to your departure date.</p>
                        
                        <p style='margin-top: 30px; font-size: 14px; color: #666;'>
                            If you have any questions about your booking, please contact our customer service team.
                        </p>
                        
                        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                        <p style='font-size: 12px; color: #999; text-align: center;'>
                            Travel & Tourism - Your Adventure Starts Here
                        </p>
                    </div>
                </body>
                </html>";

            await SendEmailAsync(to, subject, body);
        }

        public async Task SendWelcomeEmailAsync(string to, string firstName)
        {
            var subject = "Welcome to Travel & Tourism!";
            var body = $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c5aa0;'>Welcome aboard, {firstName}!</h2>
                        <p>Your email has been successfully verified and your account is now active.</p>
                        
                        <p>You can now:</p>
                        <ul>
                            <li>Browse our exciting travel destinations</li>
                            <li>Book amazing trips and tours</li>
                            <li>Read our travel blogs for inspiration</li>
                            <li>Manage your bookings and profile</li>
                        </ul>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='#' 
                               style='background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                                Explore Destinations
                            </a>
                        </div>
                        
                        <p>Thank you for choosing Travel & Tourism for your adventures!</p>
                        
                        <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
                        <p style='font-size: 12px; color: #999; text-align: center;'>
                            Travel & Tourism - Your Adventure Starts Here
                        </p>
                    </div>
                </body>
                </html>";

            await SendEmailAsync(to, subject, body);
        }
    }
}
