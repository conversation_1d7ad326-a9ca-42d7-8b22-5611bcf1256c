using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.IO;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Infrastructure.Services
{
    public class FileStorageService : IFileStorageService
    {
        private readonly Cloudinary _cloudinary;
        private readonly ILogger<FileStorageService> _logger;
        private readonly string[] _imageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" };
        private readonly long _maxFileSize = 10 * 1024 * 1024; // 10MB

        public FileStorageService(IConfiguration configuration, ILogger<FileStorageService> logger)
        {
            _logger = logger;
            
            var cloudinarySettings = configuration.GetSection("FileStorage:CloudinarySettings");
            var account = new Account(
                cloudinarySettings["CloudName"],
                cloudinarySettings["ApiKey"],
                cloudinarySettings["ApiSecret"]
            );
            
            _cloudinary = new Cloudinary(account);
        }

        public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string folder = null)
        {
            try
            {
                if (fileStream.Length > _maxFileSize)
                {
                    throw new ArgumentException($"File size exceeds maximum allowed size of {_maxFileSize / (1024 * 1024)}MB");
                }

                var uploadParams = new RawUploadParams()
                {
                    File = new FileDescription(fileName, fileStream),
                    PublicId = GeneratePublicId(fileName, folder),
                    Folder = folder
                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                if (uploadResult.Error != null)
                {
                    _logger.LogError($"Cloudinary upload error: {uploadResult.Error.Message}");
                    throw new Exception($"File upload failed: {uploadResult.Error.Message}");
                }

                _logger.LogInformation($"File uploaded successfully: {uploadResult.SecureUrl}");
                return uploadResult.SecureUrl.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading file {fileName}");
                throw;
            }
        }

        public async Task<string> UploadImageAsync(Stream imageStream, string fileName, string folder = null)
        {
            try
            {
                if (!IsImageFile(fileName))
                {
                    throw new ArgumentException("File is not a valid image format");
                }

                if (imageStream.Length > _maxFileSize)
                {
                    throw new ArgumentException($"Image size exceeds maximum allowed size of {_maxFileSize / (1024 * 1024)}MB");
                }

                var uploadParams = new ImageUploadParams()
                {
                    File = new FileDescription(fileName, imageStream),
                    PublicId = GeneratePublicId(fileName, folder),
                    Folder = folder,
                    Transformation = new Transformation()
                        .Quality("auto")
                        .FetchFormat("auto")
                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                if (uploadResult.Error != null)
                {
                    _logger.LogError($"Cloudinary image upload error: {uploadResult.Error.Message}");
                    throw new Exception($"Image upload failed: {uploadResult.Error.Message}");
                }

                _logger.LogInformation($"Image uploaded successfully: {uploadResult.SecureUrl}");
                return uploadResult.SecureUrl.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading image {fileName}");
                throw;
            }
        }

        public async Task<string> UploadDocumentAsync(Stream documentStream, string fileName, string folder = null)
        {
            try
            {
                if (documentStream.Length > _maxFileSize)
                {
                    throw new ArgumentException($"Document size exceeds maximum allowed size of {_maxFileSize / (1024 * 1024)}MB");
                }

                var uploadParams = new RawUploadParams()
                {
                    File = new FileDescription(fileName, documentStream),
                    PublicId = GeneratePublicId(fileName, folder),
                    Folder = folder
                };

                var uploadResult = await _cloudinary.UploadAsync(uploadParams);

                if (uploadResult.Error != null)
                {
                    _logger.LogError($"Cloudinary document upload error: {uploadResult.Error.Message}");
                    throw new Exception($"Document upload failed: {uploadResult.Error.Message}");
                }

                _logger.LogInformation($"Document uploaded successfully: {uploadResult.SecureUrl}");
                return uploadResult.SecureUrl.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading document {fileName}");
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string fileUrl)
        {
            try
            {
                // Extract public ID from Cloudinary URL
                var uri = new Uri(fileUrl);
                var pathSegments = uri.AbsolutePath.Split('/');
                var publicIdWithExtension = pathSegments.Last();
                var publicId = Path.GetFileNameWithoutExtension(publicIdWithExtension);

                var deletionParams = new DeletionParams(publicId)
                {
                    ResourceType = IsImageFile(publicIdWithExtension) ? ResourceType.Image : ResourceType.Raw
                };

                var deletionResult = await _cloudinary.DestroyAsync(deletionParams);

                if (deletionResult.Result == "ok")
                {
                    _logger.LogInformation($"File deleted successfully: {fileUrl}");
                    return true;
                }
                else
                {
                    _logger.LogWarning($"File deletion failed: {deletionResult.Result}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting file {fileUrl}");
                return false;
            }
        }

        public async Task<string> GetFileUrlAsync(string fileName, string folder = null)
        {
            try
            {
                var publicId = GeneratePublicId(fileName, folder);
                
                string url;
                if (IsImageFile(fileName))
                {
                    url = _cloudinary.Api.UrlImgUp.BuildUrl(publicId);
                }
                else
                {
                    url = _cloudinary.Api.UrlVideoUp.ResourceType("raw").BuildUrl(publicId);
                }
                
                return await Task.FromResult(url);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting file URL for {fileName}");
                throw;
            }
        }

        public async Task<string> OptimizeImageAsync(string imageUrl, int? width = null, int? height = null, string format = "auto")
        {
            try
            {
                var uri = new Uri(imageUrl);
                var pathSegments = uri.AbsolutePath.Split('/');
                var publicIdWithExtension = pathSegments.Last();
                var publicId = Path.GetFileNameWithoutExtension(publicIdWithExtension);

                var transformation = new Transformation().Quality("auto").FetchFormat(format);
                
                if (width.HasValue)
                    transformation = transformation.Width(width.Value);
                
                if (height.HasValue)
                    transformation = transformation.Height(height.Value);

                var optimizedUrl = _cloudinary.Api.UrlImgUp.Transform(transformation).BuildUrl(publicId);
                return await Task.FromResult(optimizedUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error optimizing image {imageUrl}");
                return imageUrl;
            }
        }

        public async Task<List<string>> GetFilesInFolderAsync(string folder)
        {
            try
            {
                var listParams = new ListResourcesParams
                {
                    Type = "upload",
                    MaxResults = 100
                };

                var result = await _cloudinary.ListResourcesAsync(listParams);
                return result.Resources.Where(r => r.PublicId.StartsWith(folder)).Select(r => r.SecureUrl.ToString()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting files from folder {folder}");
                return new List<string>();
            }
        }

        public bool IsImageFile(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return false;

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return _imageExtensions.Contains(extension);
        }

        public string GetContentType(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "application/octet-stream";

            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".webp" => "image/webp",
                ".bmp" => "image/bmp",
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt" => "text/plain",
                _ => "application/octet-stream"
            };
        }

        private string GeneratePublicId(string fileName, string folder = null)
        {
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var publicId = $"{fileNameWithoutExtension}_{timestamp}";
            
            if (!string.IsNullOrEmpty(folder))
            {
                publicId = $"{folder}/{publicId}";
            }
            
            return publicId;
        }
    }
}
