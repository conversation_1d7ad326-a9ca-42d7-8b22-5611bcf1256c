using System.IO;
using System.Threading.Tasks;

namespace TravelTourism.Core.Interfaces.Services
{
    public interface IFileStorageService
    {
        Task<string> UploadFileAsync(Stream fileStream, string fileName, string folder = null);
        Task<string> UploadImageAsync(Stream imageStream, string fileName, string folder = null);
        Task<string> UploadDocumentAsync(Stream documentStream, string fileName, string folder = null);
        Task<bool> DeleteFileAsync(string fileUrl);
        Task<string> GetFileUrlAsync(string fileName, string folder = null);
        Task<string> OptimizeImageAsync(string imageUrl, int? width = null, int? height = null, string format = "auto");
        Task<List<string>> GetFilesInFolderAsync(string folder);
        bool IsImageFile(string fileName);
        string GetContentType(string fileName);
    }
}
