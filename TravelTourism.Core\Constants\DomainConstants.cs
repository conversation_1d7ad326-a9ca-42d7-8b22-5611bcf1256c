namespace TravelTourism.Core.Constants
{
    public static class DomainConstants
    {
        public static class Cache
        {
            public const int DefaultExpirationMinutes = 60;
            public const int ShortExpirationMinutes = 15;
            public const int LongExpirationMinutes = 1440; // 24 hours
            
            public const string FeaturedTripsKey = "featured_trips";
            public const string PopularDestinationsKey = "popular_destinations";
            public const string TripCategoriesKey = "trip_categories";
            public const string BlogCategoriesKey = "blog_categories";
            public const string CountriesKey = "countries";
        }

        public static class Email
        {
            public const int VerificationTokenExpirationHours = 24;
            public const int PasswordResetTokenExpirationHours = 1;
        }

        public static class File
        {
            public const int MaxFileSizeMB = 10;
            public const int MaxImageSizeMB = 5;
            
            public static readonly string[] AllowedImageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
            public static readonly string[] AllowedFileExtensions = { ".pdf", ".doc", ".docx", ".txt" };
        }

        public static class Pagination
        {
            public const int DefaultPageSize = 10;
            public const int MaxPageSize = 100;
        }

        public static class Trip
        {
            public const int MinDuration = 1;
            public const int MaxDuration = 365;
            public const int MinCapacity = 1;
            public const int MaxCapacity = 1000;
        }

        public static class Booking
        {
            public const int MaxAdvanceBookingDays = 365;
            public const int MinAdvanceBookingDays = 1;
        }
    }
}
