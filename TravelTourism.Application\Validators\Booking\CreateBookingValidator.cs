using FluentValidation;
using TravelTourism.Application.DTOs.Booking;

namespace TravelTourism.Application.Validators.Booking;

public class CreateBookingValidator : AbstractValidator<CreateBookingDto>
{
    public CreateBookingValidator()
    {
        RuleFor(x => x.TripId)
            .GreaterThan(0)
            .WithMessage("Trip ID must be greater than 0");

        RuleFor(x => x.TravelDate)
            .NotEmpty()
            .WithMessage("Travel date is required")
            .GreaterThan(DateTime.Today)
            .WithMessage("Travel date must be in the future");

        RuleFor(x => x.NumberOfAdults)
            .GreaterThan(0)
            .WithMessage("At least one adult is required");

        RuleFor(x => x.NumberOfChildren)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Number of children cannot be negative");

        RuleFor(x => x.NumberOfInfants)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Number of infants cannot be negative");

        RuleFor(x => x.<PERSON><PERSON>er<PERSON>erson)
            .GreaterThan(0)
            .WithMessage("Price per person must be greater than 0");

        RuleFor(x => x.EmergencyContactName)
            .NotEmpty()
            .WithMessage("Emergency contact name is required")
            .MaximumLength(100)
            .WithMessage("Emergency contact name cannot exceed 100 characters");

        RuleFor(x => x.EmergencyContactPhone)
            .NotEmpty()
            .WithMessage("Emergency contact phone is required")
            .MaximumLength(20)
            .WithMessage("Emergency contact phone cannot exceed 20 characters")
            .Matches(@"^\+?[1-9]\d{1,14}$")
            .WithMessage("Invalid phone number format");

        RuleFor(x => x.SpecialRequests)
            .MaximumLength(1000)
            .WithMessage("Special requests cannot exceed 1000 characters");

        RuleFor(x => x.TotalPeople)
            .GreaterThan(0)
            .WithMessage("Total number of people must be greater than 0")
            .LessThanOrEqualTo(50)
            .WithMessage("Total number of people cannot exceed 50");
    }
} 