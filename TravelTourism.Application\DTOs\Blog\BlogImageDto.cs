using System.ComponentModel.DataAnnotations;

namespace TravelTourism.Application.DTOs.Blog;

public class BlogImageDto
{
    public int Id { get; set; }
    public int BlogId { get; set; }
    
    [Required]
    [MaxLength(500)]
    public string ImageUrl { get; set; } = string.Empty;
    
    [MaxLength(200)]
    public string? Caption { get; set; }
    public int DisplayOrder { get; set; }
    public DateTime CreatedAt { get; set; }
} 