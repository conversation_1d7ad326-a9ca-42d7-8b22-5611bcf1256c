using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.TripSpecifications
{
    public class FeaturedTripsSpecification : BaseSpecification<Trip>
    {
        public FeaturedTripsSpecification(int count = 10) 
            : base(x => x.IsFeatured && x.IsActive && !x.IsDeleted)
        {
            AddInclude(x => x.Category);
            AddInclude(x => x.DestinationCity);
            AddInclude(x => x.Images);
            AddOrderBy(x => x.CreatedAt);
            ApplyPaging(0, count);
        }
    }
}
