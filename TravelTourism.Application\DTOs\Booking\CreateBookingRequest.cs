using System.ComponentModel.DataAnnotations;

namespace TravelTourism.Application.DTOs.Booking;

public class CreateBookingRequest
{
    [Required]
    public int TripId { get; set; }
    
    [Required]
    public DateTime TravelDate { get; set; }
    
    [Required]
    [Range(1, 100)]
    public int NumberOfAdults { get; set; } = 1;
    
    [Range(0, 100)]
    public int NumberOfChildren { get; set; } = 0;
    
    [Range(0, 100)]
    public int NumberOfInfants { get; set; } = 0;
    
    [Required]
    [Range(0, double.MaxValue)]
    public decimal PricePerPerson { get; set; }
    
    [MaxLength(1000)]
    public string SpecialRequests { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string EmergencyContactName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(20)]
    public string EmergencyContactPhone { get; set; } = string.Empty;
    
    // Computed property
    public int TotalPeople => NumberOfAdults + NumberOfChildren + NumberOfInfants;
} 