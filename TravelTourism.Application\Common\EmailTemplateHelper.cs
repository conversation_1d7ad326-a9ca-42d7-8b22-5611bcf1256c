namespace TravelTourism.Application.Common;

public static class EmailTemplateHelper
{
    public static string GetEmailVerificationTemplate(string token, string email)
    {
        return $@"
            <h2>Email Verification</h2>
            <p>Please click the link below to verify your email address:</p>
            <a href='https://yourdomain.com/verify-email?token={token}&email={email}'>Verify Email</a>
        ";
    }

    public static string GetPasswordResetTemplate(string token, string email)
    {
        return $@"
            <h2>Password Reset</h2>
            <p>Please click the link below to reset your password:</p>
            <a href='https://yourdomain.com/reset-password?token={token}&email={email}'>Reset Password</a>
        ";
    }

    public static string GetBookingConfirmationTemplate(string bookingNumber, string tripName, DateTime travelDate)
    {
        return $@"
            <h2>Booking Confirmation</h2>
            <p>Your booking has been confirmed!</p>
            <p>Booking Number: {bookingNumber}</p>
            <p>Trip: {tripName}</p>
            <p>Travel Date: {travelDate:dd/MM/yyyy}</p>
        ";
    }
} 