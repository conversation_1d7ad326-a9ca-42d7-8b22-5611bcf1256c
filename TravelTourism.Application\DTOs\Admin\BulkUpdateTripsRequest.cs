namespace TravelTourism.Application.DTOs.Admin
{
    public class BulkUpdateTripsRequest
    {
        public List<int> TripIds { get; set; } = new();
        public bool? IsActive { get; set; }
        public bool? IsFeatured { get; set; }
        public int? CategoryId { get; set; }
        public decimal? PriceAdjustment { get; set; }
        public string? PriceAdjustmentType { get; set; } // "percentage", "fixed"
    }
} 