using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.DTOs.Booking;
using TravelTourism.Application.Interfaces;
using TravelTourism.Core.Interfaces.Services;



namespace TravelTourism.API.Controllers.V1
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class BookingsController : BaseController
    {
        private readonly IBookingService _bookingService;
        private readonly IPaymentService _paymentService;

        public BookingsController(IBookingService bookingService, IPaymentService paymentService)
        {
            _bookingService = bookingService;
            _paymentService = paymentService;
        }

        /// <summary>
        /// Create a new booking
        /// </summary>
        /// <param name="request">Booking creation request</param>
        /// <returns>Created booking details</returns>
        [HttpPost]
        public async Task<IActionResult> CreateBooking([FromBody] CreateBookingRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var userId = GetCurrentUserId();
                var booking = await _bookingService.CreateBookingAsync(request, userId);

                return CreatedAtAction(
                    nameof(GetBooking),
                    new { id = booking.Id },
                    CreateSuccessResponse(booking, "Booking created successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking by ID
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Booking details</returns>
        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetBooking(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var booking = await _bookingService.GetBookingByIdAsync(id, userId);

                if (booking == null)
                {
                    return NotFound(CreateErrorResponse("Booking not found", "BOOKING_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(booking, "Booking retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Cancel a booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Cancellation result</returns>
        [HttpDelete("{id:int}")]
        public async Task<IActionResult> CancelBooking(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _bookingService.CancelBookingAsync(id, userId);

                if (!result)
                {
                    return BadRequest(CreateErrorResponse("Unable to cancel booking", "CANCEL_FAILED"));
                }

                return Ok(CreateSuccessResponse<object>(null, "Booking cancelled successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Process payment for a booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Payment request</param>
        /// <returns>Payment result</returns>
        [HttpPost("{id:int}/payment")]
        public async Task<IActionResult> ProcessPayment(int id, [FromBody] TravelTourism.Core.Interfaces.Services.PaymentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var userId = GetCurrentUserId();
                request.UserId = userId;
                var result = await _paymentService.ProcessPaymentAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, "PAYMENT_FAILED"));
                }

                return Ok(CreateSuccessResponse(result, "Payment processed successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get payment status for a booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="paymentIntentId">Payment Intent ID</param>
        /// <returns>Payment status</returns>
        [HttpGet("{id:int}/payment/status")]
        public async Task<IActionResult> GetPaymentStatus(int id, [FromQuery] string paymentIntentId)
        {
            try
            {
                if (string.IsNullOrEmpty(paymentIntentId))
                {
                    return BadRequest(CreateErrorResponse("Payment Intent ID is required", "MISSING_PAYMENT_INTENT"));
                }

                var status = await _paymentService.GetPaymentStatusAsync(paymentIntentId);

                if (!status.Success)
                {
                    return NotFound(CreateErrorResponse("Payment not found", "PAYMENT_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(status, "Payment status retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Confirm payment for a booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Payment confirmation request</param>
        /// <returns>Payment confirmation result</returns>
        [HttpPost("{id:int}/payment/confirm")]
        public async Task<IActionResult> ConfirmPayment(int id, [FromBody] PaymentConfirmationRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _paymentService.ConfirmPaymentAsync(request.TransactionId);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, "PAYMENT_CONFIRMATION_FAILED"));
                }

                return Ok(CreateSuccessResponse(result, "Payment confirmed successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Request refund for a booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Refund request</param>
        /// <returns>Refund result</returns>
        [HttpPost("{id:int}/refund")]
        public async Task<IActionResult> RequestRefund(int id, [FromBody] TravelTourism.Application.DTOs.Booking.RefundRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var userId = GetCurrentUserId();
                var coreRefundRequest = new TravelTourism.Core.Interfaces.Services.RefundRequest
                {
                    Amount = request.RefundAmount,
                    Reason = request.Reason,
                    Currency = "USD"
                };
                var result = await _paymentService.RequestRefundAsync(id, userId, coreRefundRequest);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, "REFUND_FAILED"));
                }

                return Ok(CreateSuccessResponse(result, "Refund requested successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking invoice
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Invoice PDF</returns>
        [HttpGet("{id:int}/invoice")]
        public async Task<IActionResult> GetInvoice(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var invoice = await _bookingService.GenerateInvoiceAsync(id, userId);

                if (invoice == null)
                {
                    return NotFound(CreateErrorResponse("Invoice not found", "INVOICE_NOT_FOUND"));
                }

                return File(invoice.Data, "application/pdf", $"invoice-{id}.pdf");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update booking details
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Update booking request</param>
        /// <returns>Updated booking details</returns>
        [HttpPut("{id:int}")]
        public async Task<IActionResult> UpdateBooking(int id, [FromBody] UpdateBookingRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var userId = GetCurrentUserId();
                var result = await _bookingService.UpdateBookingAsync(id, request, userId);

                if (!result)
                {
                    return BadRequest(CreateErrorResponse("Unable to update booking", "UPDATE_FAILED"));
                }

                return Ok(CreateSuccessResponse(null, "Booking updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}

