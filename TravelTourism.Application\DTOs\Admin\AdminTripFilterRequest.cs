namespace TravelTourism.Application.DTOs.Admin
{
    public class AdminTripFilterRequest
    {
        public string? SearchTerm { get; set; }
        public int? CategoryId { get; set; }
        public int? CityId { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public string? Difficulty { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsFeatured { get; set; }
        public DateTime? StartDateFrom { get; set; }
        public DateTime? StartDateTo { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }
} 