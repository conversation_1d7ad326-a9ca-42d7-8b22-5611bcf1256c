using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TravelTourism.Core.Entities.Booking;

namespace TravelTourism.Infrastructure.Data.Configurations;

public class BookingPaymentConfiguration : IEntityTypeConfiguration<BookingPayment>
{
    public void Configure(EntityTypeBuilder<BookingPayment> builder)
    {
        builder.ToTable("BookingPayments");

        builder.HasKey(bp => bp.Id);

        builder.Property(bp => bp.PaymentMethod)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(bp => bp.TransactionId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(bp => bp.Amount)
            .HasColumnType("decimal(18,2)");

        builder.Property(bp => bp.Currency)
            .IsRequired()
            .HasMaxLength(3)
            .HasDefaultValue("USD");

        builder.Property(bp => bp.FailureReason)
            .HasMaxLength(500);

        builder.Property(bp => bp.RefundAmount)
            .HasColumnType("decimal(18,2)");

        // Relationships
        builder.HasOne(bp => bp.Booking)
            .WithMany(b => b.Payments)
            .HasForeignKey(bp => bp.BookingId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
