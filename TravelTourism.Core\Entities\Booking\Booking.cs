using System;
using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;
using TravelTourism.Core.Enums;

namespace TravelTourism.Core.Entities.Booking
{
    public class Booking : BaseEntity
    {
        public string BookingNumber { get; set; } = string.Empty;
        public int UserId { get; set; }
        public int TripId { get; set; }
        public DateTime BookingDate { get; set; } = DateTime.UtcNow;
        public DateTime TravelDate { get; set; }
        public int NumberOfAdults { get; set; } = 1;
        public int NumberOfChildren { get; set; } = 0;
        public int NumberOfInfants { get; set; } = 0;
        public decimal PricePerPerson { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal FinalAmount { get; set; }
        public BookingStatus Status { get; set; } = BookingStatus.Pending;
        public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
        public string SpecialRequests { get; set; } = string.Empty;
        public string EmergencyContactName { get; set; } = string.Empty;
        public string EmergencyContactPhone { get; set; } = string.Empty;
        public string CancellationReason { get; set; } = string.Empty;
        public DateTime? CancelledAt { get; set; }
        public DateTime? ConfirmedAt { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public DateTime? PaidAt { get; set; }

        // Navigation properties
        public virtual User.User User { get; set; } = null!;
        public virtual Trip.Trip Trip { get; set; } = null!;
        public virtual ICollection<BookingPayment> Payments { get; set; } = new List<BookingPayment>();

        public int TotalPeople => NumberOfAdults + NumberOfChildren + NumberOfInfants;

        public bool CanBeCancelled => Status == BookingStatus.Pending || Status == BookingStatus.Confirmed;

        public void Cancel(string? reason = null)
        {
            if (!CanBeCancelled)
                throw new InvalidOperationException("Booking cannot be cancelled in current status");

            Status = BookingStatus.Cancelled;
            CancellationReason = reason ?? string.Empty;
            CancelledAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Confirm()
        {
            if (Status != BookingStatus.Pending)
                throw new InvalidOperationException("Only pending bookings can be confirmed");

            Status = BookingStatus.Confirmed;
            ConfirmedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Complete()
        {
            if (Status != BookingStatus.Confirmed)
                throw new InvalidOperationException("Only confirmed bookings can be completed");

            Status = BookingStatus.Completed;
            UpdatedAt = DateTime.UtcNow;
        }

        public string GenerateBookingNumber()
        {
            return $"BK{DateTime.UtcNow:yyyyMMdd}{Id:D6}";
        }
    }
}
