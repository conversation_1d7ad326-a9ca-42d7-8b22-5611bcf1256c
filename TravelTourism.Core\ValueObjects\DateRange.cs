using System;

namespace TravelTourism.Core.ValueObjects
{
    public class DateRange
    {
        public DateTime StartDate { get; private set; }
        public DateTime EndDate { get; private set; }

        public DateRange(DateTime startDate, DateTime endDate)
        {
            if (startDate >= endDate)
                throw new ArgumentException("Start date must be before end date");

            StartDate = startDate;
            EndDate = endDate;
        }

        public int DurationInDays => (int)(EndDate - StartDate).TotalDays;

        public bool Contains(DateTime date)
        {
            return date >= StartDate && date <= EndDate;
        }

        public bool Overlaps(DateRange other)
        {
            return StartDate <= other.EndDate && EndDate >= other.StartDate;
        }

        public override string ToString()
        {
            return $"{StartDate:yyyy-MM-dd} to {EndDate:yyyy-MM-dd}";
        }

        public override bool Equals(object obj)
        {
            return obj is DateRange range &&
                   StartDate == range.StartDate &&
                   EndDate == range.EndDate;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(StartDate, EndDate);
        }
    }
}
