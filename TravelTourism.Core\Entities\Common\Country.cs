using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;

namespace TravelTourism.Core.Entities.Common
{
    public class Country : BaseEntity
    {
        public string Name { get; set; }
        public string Code { get; set; }
        public string Currency { get; set; }
        public string CurrencyCode { get; set; }
        public string CurrencySymbol { get; set; }
        public string FlagUrl { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<City> Cities { get; set; } = new List<City>();
    }
}
