using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TravelTourism.Core.Entities.Trip;

namespace TravelTourism.Infrastructure.Data.Configurations;

public class TripConfiguration : IEntityTypeConfiguration<Trip>
{
    public void Configure(EntityTypeBuilder<Trip> builder)
    {
        builder.ToTable("Trips");

        builder.HasKey(t => t.Id);

        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(t => t.Description)
            .IsRequired();

        builder.Property(t => t.ShortDescription)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(t => t.Price)
            .HasColumnType("decimal(18,2)");

        builder.Property(t => t.DiscountPrice)
            .HasColumnType("decimal(18,2)");

        builder.Property(t => t.MainImageUrl)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(t => t.IsActive)
            .HasDefaultValue(true);

        builder.Property(t => t.IsFeatured)
            .HasDefaultValue(false);

        // Relationships
        builder.HasOne(t => t.Category)
            .WithMany(c => c.Trips)
            .HasForeignKey(t => t.CategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure the two relationships with City
        builder.HasOne(t => t.DestinationCity)
            .WithMany(c => c.DestinationTrips)
            .HasForeignKey(t => t.DestinationCityId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(t => t.DepartureCity)
            .WithMany(c => c.DepartureTrips)
            .HasForeignKey(t => t.DepartureCityId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(t => t.Images)
            .WithOne(i => i.Trip)
            .HasForeignKey(i => i.TripId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.Itineraries)
            .WithOne(i => i.Trip)
            .HasForeignKey(i => i.TripId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(t => t.Bookings)
            .WithOne(b => b.Trip)
            .HasForeignKey(b => b.TripId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}