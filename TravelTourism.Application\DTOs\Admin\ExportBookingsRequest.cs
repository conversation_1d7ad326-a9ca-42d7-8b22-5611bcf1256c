namespace TravelTourism.Application.DTOs.Admin
{
    public class ExportBookingsRequest
    {
        public string Format { get; set; } = "csv"; // "csv", "excel", "pdf"
        public string? SearchTerm { get; set; }
        public int? TripId { get; set; }
        public int? UserId { get; set; }
        public string? Status { get; set; }
        public DateTime? BookingDateFrom { get; set; }
        public DateTime? BookingDateTo { get; set; }
        public DateTime? TripDateFrom { get; set; }
        public DateTime? TripDateTo { get; set; }
        public List<string> Fields { get; set; } = new();
    }
} 