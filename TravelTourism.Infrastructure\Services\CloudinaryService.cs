using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Infrastructure.Services;

public class CloudinaryService : IFileStorageService
{
    private readonly Cloudinary _cloudinary;
    private readonly ILogger<CloudinaryService> _logger;

    public CloudinaryService(IConfiguration configuration, ILogger<CloudinaryService> logger)
    {
        _logger = logger;
        var cloudinarySettings = configuration.GetSection("FileStorage:CloudinarySettings");
        var account = new Account(
            cloudinarySettings["CloudName"],
            cloudinarySettings["ApiKey"],
            cloudinarySettings["ApiSecret"]
        );
        _cloudinary = new Cloudinary(account);
    }

    public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string folder)
    {
        try
        {
            var uploadParams = new RawUploadParams
            {
                File = new FileDescription(fileName, fileStream),
                PublicId = $"{folder}/{Path.GetFileNameWithoutExtension(fileName)}_{Guid.NewGuid()}",
                Overwrite = false
            };

            var result = await _cloudinary.UploadAsync(uploadParams);
            _logger.LogInformation("File uploaded successfully: {FileName} to {Url}", fileName, result.SecureUrl);
            return result.SecureUrl.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file: {FileName}", fileName);
            throw;
        }
    }

    public async Task<string> UploadImageAsync(Stream imageStream, string fileName, string folder)
    {
        try
        {
            var uploadParams = new ImageUploadParams
            {
                File = new FileDescription(fileName, imageStream),
                PublicId = $"{folder}/{Path.GetFileNameWithoutExtension(fileName)}_{Guid.NewGuid()}",
                Overwrite = false,
                Transformation = new Transformation().Quality("auto").FetchFormat("auto")
            };

            var result = await _cloudinary.UploadAsync(uploadParams);
            _logger.LogInformation("Image uploaded successfully: {FileName} to {Url}", fileName, result.SecureUrl);
            return result.SecureUrl.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading image: {FileName}", fileName);
            throw;
        }
    }

    public async Task<string> UploadDocumentAsync(Stream documentStream, string fileName, string folder)
    {
        try
        {
            var uploadParams = new RawUploadParams
            {
                File = new FileDescription(fileName, documentStream),
                PublicId = $"{folder}/{Path.GetFileNameWithoutExtension(fileName)}_{Guid.NewGuid()}",
                Overwrite = false
            };

            var result = await _cloudinary.UploadAsync(uploadParams);
            _logger.LogInformation("Document uploaded successfully: {FileName} to {Url}", fileName, result.SecureUrl);
            return result.SecureUrl.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading document: {FileName}", fileName);
            throw;
        }
    }

    public async Task<bool> DeleteFileAsync(string fileUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(fileUrl))
                return false;

            var publicId = ExtractPublicIdFromUrl(fileUrl);
            if (string.IsNullOrEmpty(publicId))
                return false;

            var deleteParams = new DeletionParams(publicId);
            var result = await _cloudinary.DestroyAsync(deleteParams);
            
            _logger.LogInformation("File deleted successfully: {PublicId}", publicId);
            return result.Result == "ok";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file: {FileUrl}", fileUrl);
            return false;
        }
    }

    public Task<string> GetFileUrlAsync(string fileId, string? format = null)
    {
        try
        {
            var url = _cloudinary.Api.UrlImgUp.BuildUrl(fileId);
            if (!string.IsNullOrEmpty(format))
            {
                url = _cloudinary.Api.UrlImgUp.Transform(new Transformation().FetchFormat(format)).BuildUrl(fileId);
            }
            return Task.FromResult(url);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file URL: {FileId}", fileId);
            return Task.FromResult(string.Empty);
        }
    }

    public Task<string> OptimizeImageAsync(string imageUrl, int? width = null, int? height = null, string format = "auto")
    {
        try
        {
            var publicId = ExtractPublicIdFromUrl(imageUrl);
            if (string.IsNullOrEmpty(publicId))
                return Task.FromResult(imageUrl);

            var transformation = new Transformation().Quality("auto").FetchFormat(format);
            
            if (width.HasValue)
                transformation = transformation.Width(width.Value);
            
            if (height.HasValue)
                transformation = transformation.Height(height.Value);

            var optimizedUrl = _cloudinary.Api.UrlImgUp.Transform(transformation).BuildUrl(publicId);
            return Task.FromResult(optimizedUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing image: {ImageUrl}", imageUrl);
            return Task.FromResult(imageUrl);
        }
    }

    public bool IsImageFile(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" }.Contains(extension);
    }

    public string GetContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            ".webp" => "image/webp",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".txt" => "text/plain",
            _ => "application/octet-stream"
        };
    }

    public async Task<List<string>> GetFilesInFolderAsync(string folder)
    {
        try
        {
            var listParams = new ListResourcesParams
            {
                Type = "upload",
                MaxResults = 100
            };

            var result = await _cloudinary.ListResourcesAsync(listParams);
            return result.Resources.Where(r => r.PublicId.StartsWith(folder)).Select(r => r.SecureUrl.ToString()).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting files from folder: {Folder}", folder);
            return new List<string>();
        }
    }

    private string? ExtractPublicIdFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            var segments = uri.Segments;
            if (segments.Length < 2)
                return null;

            // Extract public ID from Cloudinary URL
            var publicId = string.Join("", segments.Skip(1)).TrimEnd('/');
            return publicId;
        }
        catch
        {
            return null;
        }
    }
} 