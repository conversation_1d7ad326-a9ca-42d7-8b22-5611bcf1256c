using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.API.Models.Requests;
using TravelTourism.Application.DTOs.Admin;
using TravelTourism.Application.DTOs.Blog;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;

namespace TravelTourism.Controllers.Admin 
{
    [ApiController]
    [Route("api/v1/admin/[controller]")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class AdminBlogsController : BaseController
    {
        private readonly IBlogService _blogService;
        private readonly IAdminService _adminService;

        public AdminBlogsController(IBlogService blogService, IAdminService adminService)
        {
            _blogService = blogService;
            _adminService = adminService;
        }

        /// <summary>
        /// Get all blogs with pagination and filtering
        /// </summary>
        /// <param name="request">Blogs filter and pagination request</param>
        /// <returns>Paginated list of blogs</returns>
        [HttpGet]
        public async Task<IActionResult> GetBlogs([FromQuery] AdminBlogFilterRequest request)
        {
            try
            {
                var blogs = await _adminService.GetBlogsAsync(request);
                return Ok(CreateSuccessResponse(blogs, "Blogs retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get blog by ID
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Blog details</returns>
        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetBlog(int id)
        {
            try
            {
                var blog = await _blogService.GetBlogByIdAsync(id);

                if (blog == null)
                {
                    return NotFound(CreateErrorResponse("Blog not found", "BLOG_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(blog, "Blog retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create a new blog
        /// </summary>
        /// <param name="request">Blog creation request</param>
        /// <returns>Created blog details</returns>
        [HttpPost]
        public async Task<IActionResult> CreateBlog([FromBody] CreateBlogRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var authorId = GetCurrentUserId();
                var blog = await _blogService.CreateBlogAsync(request, authorId);

                return CreatedAtAction(
                    nameof(GetBlog),
                    new { id = blog.Id },
                    CreateSuccessResponse(blog, "Blog created successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update blog details
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <param name="request">Update blog request</param>
        /// <returns>Updated blog details</returns>
        [HttpPut("{id:int}")]
        public async Task<IActionResult> UpdateBlog(int id, [FromBody] UpdateBlogRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var blog = await _blogService.UpdateBlogAsync(id, request);

                if (blog == null)
                {
                    return BadRequest(CreateErrorResponse("Unable to update blog", "UPDATE_FAILED"));
                }

                return Ok(CreateSuccessResponse(blog, "Blog updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete blog
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteBlog(int id)
        {
            try
            {
                var result = await _blogService.DeleteBlogAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Blog deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Publish blog
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Publication result</returns>
        [HttpPost("{id:int}/publish")]
        public async Task<IActionResult> PublishBlog(int id)
        {
            try
            {
                var result = await _adminService.PublishBlogAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Blog published successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Unpublish blog
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Unpublication result</returns>
        [HttpPost("{id:int}/unpublish")]
        public async Task<IActionResult> UnpublishBlog(int id)
        {
            try
            {
                var result = await _adminService.UnpublishBlogAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Blog unpublished successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Feature blog
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Feature result</returns>
        [HttpPost("{id:int}/feature")]
        public async Task<IActionResult> FeatureBlog(int id)
        {
            try
            {
                var result = await _adminService.FeatureBlogAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Blog featured successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Unfeature blog
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Unfeature result</returns>
        [HttpPost("{id:int}/unfeature")]
        public async Task<IActionResult> UnfeatureBlog(int id)
        {
            try
            {
                var result = await _adminService.UnfeatureBlogAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Blog unfeatured successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get blog statistics
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Blog statistics</returns>
        [HttpGet("{id:int}/statistics")]
        public async Task<IActionResult> GetBlogStatistics(int id)
        {
            try
            {
                var statistics = await _adminService.GetBlogStatisticsAsync(id);
                return Ok(CreateSuccessResponse(statistics, "Blog statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Upload blog cover image
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <param name="file">Cover image file</param>
        /// <returns>Upload result</returns>
        [HttpPost("{id:int}/cover-image")]
        public async Task<IActionResult> UploadBlogCoverImage(int id, IFormFile file)
        {
            try
            {
                if (file == null)
                {
                    return BadRequest(CreateErrorResponse("No file provided", "NO_FILE"));
                }

                var result = await _adminService.UploadBlogCoverImageAsync(id, file);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Cover image uploaded successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete blog cover image
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}/cover-image")]
        public async Task<IActionResult> DeleteBlogCoverImage(int id)
        {
            try
            {
                var result = await _adminService.DeleteBlogCoverImageAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Cover image deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get blog comments
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <param name="request">Pagination request</param>
        /// <returns>Blog comments</returns>
        [HttpGet("{id:int}/comments")]
        public async Task<IActionResult> GetBlogComments(int id, [FromQuery] PaginationRequest request)
        {
            try
            {
                var pagination = new PaginationParameters
                {
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
                var comments = await _adminService.GetBlogCommentsAsync(id, pagination);
                return Ok(CreateSuccessResponse(comments, "Blog comments retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Approve blog comment
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <param name="commentId">Comment ID</param>
        /// <returns>Approval result</returns>
        [HttpPost("{id:int}/comments/{commentId:int}/approve")]
        public async Task<IActionResult> ApproveBlogComment(int id, int commentId)
        {
            try
            {
                var result = await _adminService.ApproveBlogCommentAsync(id, commentId);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Comment approved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Reject blog comment
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <param name="commentId">Comment ID</param>
        /// <returns>Rejection result</returns>
        [HttpPost("{id:int}/comments/{commentId:int}/reject")]
        public async Task<IActionResult> RejectBlogComment(int id, int commentId)
        {
            try
            {
                var result = await _adminService.RejectBlogCommentAsync(id, commentId);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Comment rejected successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete blog comment
        /// </summary>
        /// <param name="id">Blog ID</param>
        /// <param name="commentId">Comment ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}/comments/{commentId:int}")]
        public async Task<IActionResult> DeleteBlogComment(int id, int commentId)
        {
            try
            {
                var result = await _adminService.DeleteBlogCommentAsync(id, commentId);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Comment deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get blog categories
        /// </summary>
        /// <returns>List of blog categories</returns>
        [HttpGet("categories")]
        public async Task<IActionResult> GetBlogCategories()
        {
            try
            {
                var categories = await _adminService.GetBlogCategoriesAsync();
                return Ok(CreateSuccessResponse(categories, "Blog categories retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create blog category
        /// </summary>
        /// <param name="request">Category creation request</param>
        /// <returns>Created category</returns>
        [HttpPost("categories")]
        public async Task<IActionResult> CreateBlogCategory([FromBody] CreateBlogCategoryRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var category = await _adminService.CreateBlogCategoryAsync(request);

                return CreatedAtAction(
                    nameof(GetBlogCategories),
                    null,
                    CreateSuccessResponse(category, "Blog category created successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update blog category
        /// </summary>
        /// <param name="id">Category ID</param>
        /// <param name="request">Update category request</param>
        /// <returns>Updated category</returns>
        [HttpPut("categories/{id:int}")]
        public async Task<IActionResult> UpdateBlogCategory(int id, [FromBody] UpdateBlogCategoryRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.UpdateBlogCategoryAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Blog category updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete blog category
        /// </summary>
        /// <param name="id">Category ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("categories/{id:int}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteBlogCategory(int id)
        {
            try
            {
                var result = await _adminService.DeleteBlogCategoryAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Blog category deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get blog tags
        /// </summary>
        /// <returns>List of blog tags</returns>
        [HttpGet("tags")]
        public async Task<IActionResult> GetBlogTags()
        {
            try
            {
                var tags = await _adminService.GetBlogTagsAsync();
                return Ok(CreateSuccessResponse(tags, "Blog tags retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create blog tag
        /// </summary>
        /// <param name="request">Tag creation request</param>
        /// <returns>Created tag</returns>
        [HttpPost("tags")]
        public async Task<IActionResult> CreateBlogTag([FromBody] CreateBlogTagRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var tag = await _adminService.CreateBlogTagAsync(request);

                return CreatedAtAction(
                    nameof(GetBlogTags),
                    null,
                    CreateSuccessResponse(tag, "Blog tag created successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update blog tag
        /// </summary>
        /// <param name="id">Tag ID</param>
        /// <param name="request">Update tag request</param>
        /// <returns>Updated tag</returns>
        [HttpPut("tags/{id:int}")]
        public async Task<IActionResult> UpdateBlogTag(int id, [FromBody] UpdateBlogTagRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.UpdateBlogTagAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Blog tag updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete blog tag
        /// </summary>
        /// <param name="id">Tag ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("tags/{id:int}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteBlogTag(int id)
        {
            try
            {
                var result = await _adminService.DeleteBlogTagAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Blog tag deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Bulk update blog status
        /// </summary>
        /// <param name="request">Bulk update request</param>
        /// <returns>Update result</returns>
        [HttpPost("bulk-update")]
        public async Task<IActionResult> BulkUpdateBlogs([FromBody] BulkUpdateBlogsRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.BulkUpdateBlogsAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Blogs updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Export blogs to CSV
        /// </summary>
        /// <param name="request">Export request</param>
        /// <returns>CSV file</returns>
        [HttpGet("export")]
        public async Task<IActionResult> ExportBlogs([FromQuery] ExportBlogsRequest request)
        {
            try
            {
                var result = await _adminService.ExportBlogsAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return File(result.Data, "text/csv", "blogs.csv");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}


