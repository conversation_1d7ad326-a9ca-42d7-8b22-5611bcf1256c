using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BlogSpecifications;

public class PublishedBlogsSpecification : BaseSpecification<Blog>
{
    public PublishedBlogsSpecification()
        : base(b => b.IsPublished && !b.Is<PERSON>eleted)
    {
        AddInclude(b => b.Category);
        AddInclude(b => b.Author);
        AddInclude(b => b.BlogImages);
        AddOrderByDescending(b => b.PublishedAt);
    }
} 