using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BlogSpecifications;

public class PublishedBlogsSpecification : BaseSpecification<Blog>
{
    public PublishedBlogsSpecification()
        : base(b => b.IsPublished && !b.IsDeleted)
    {
        AddInclude(b => b.Category);
        AddInclude(b => b.Author);
        AddInclude(b => b.Images);
        AddOrderByDescending(b => b.PublishedAt);
    }
} 