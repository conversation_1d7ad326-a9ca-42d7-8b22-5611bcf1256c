using System.ComponentModel.DataAnnotations;

namespace TravelTourism.Application.DTOs.Blog;

public class BlogDto
{
    public int Id { get; set; }
    
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(200)]
    public string Slug { get; set; } = string.Empty;
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(500)]
    public string Excerpt { get; set; } = string.Empty;
    
    public int AuthorId { get; set; }
    public string AuthorName { get; set; } = string.Empty;
    
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    
    public string? FeaturedImageUrl { get; set; }
    public bool IsPublished { get; set; }
    public bool IsFeatured { get; set; }
    public DateTime? PublishedAt { get; set; }
    public int ViewCount { get; set; }
    public int ReadingTime { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    public List<BlogImageDto> Images { get; set; } = new();
    public List<string> Tags { get; set; } = new();
} 