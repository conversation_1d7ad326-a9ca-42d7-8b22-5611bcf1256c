using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BlogSpecifications;

public class BlogsByCategorySpecification : BaseSpecification<Blog>
{
    public BlogsByCategorySpecification(int categoryId)
        : base(b => b.CategoryId == categoryId && b.IsPublished && !b.IsDeleted)
    {
        AddInclude(b => b.Category);
        AddInclude(b => b.Author);
        AddInclude(b => b.Images);
        AddOrderByDescending(b => b.PublishedAt);
    }
} 