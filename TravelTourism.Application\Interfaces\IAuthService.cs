using TravelTourism.Application.DTOs.Auth;
using TravelTourism.Application.DTOs.Common;

namespace TravelTourism.Application.Interfaces
{
    public interface IAuthService
    {
        Task<AuthResultDto> RegisterAsync(RegisterDto registerDto);
        Task<AuthResultDto> LoginAsync(LoginDto loginDto);
        Task<ApiResponse> VerifyEmailAsync(VerifyEmailDto verifyEmailDto);
        Task<ApiResponse> ForgotPasswordAsync(ForgotPasswordDto forgotPasswordDto);
        Task<ApiResponse> ResetPasswordAsync(ResetPasswordDto resetPasswordDto);
        Task<AuthResultDto> RefreshTokenAsync(RefreshTokenDto refreshTokenDto);
        Task<ApiResponse> LogoutAsync(int userId);
        Task<ApiResponse> RevokeAllUserTokensAsync(int userId);
    }
}
