using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BlogSpecifications;

public class FeaturedBlogsSpecification : BaseSpecification<Blog>
{
    public FeaturedBlogsSpecification()
        : base(b => b.IsFeatured && b.IsPublished && !b.IsDeleted)
    {
        AddInclude(b => b.Category);
        AddInclude(b => b.Author);
        AddInclude(b => b.BlogImages);
        AddOrderByDescending(b => b.PublishedAt);
    }
} 