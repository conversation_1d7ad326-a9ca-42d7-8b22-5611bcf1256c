using System.Collections.Generic;
using System.Threading.Tasks;
using TravelTourism.Core.Entities.Blog;

namespace TravelTourism.Core.Interfaces.Repositories
{
    public interface IBlogRepository : IGenericRepository<Blog>
    {
        Task<IReadOnlyList<Blog>> GetPublishedBlogsAsync();
        Task<IReadOnlyList<Blog>> GetFeaturedBlogsAsync(int count = 10);
        Task<Blog> GetBySlugAsync(string slug);
        Task<IReadOnlyList<Blog>> GetBlogsByCategoryAsync(int categoryId);
        Task<IReadOnlyList<Blog>> GetBlogsByAuthorAsync(int authorId);
        Task<IReadOnlyList<BlogCategory>> GetCategoriesAsync();
        Task<IReadOnlyList<BlogTag>> GetTagsAsync();
        Task<bool> IsSlugUniqueAsync(string slug, int? excludeBlogId = null);
    }
}
