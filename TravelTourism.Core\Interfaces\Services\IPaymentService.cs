using System.Threading.Tasks;

namespace TravelTourism.Core.Interfaces.Services
{
    public interface IPaymentService
    {
        Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest);
        Task<PaymentResult> ConfirmPaymentAsync(string paymentIntentId);
        Task<PaymentResult> RefundPaymentAsync(string paymentIntentId, decimal? amount = null);
        Task<PaymentResult> GetPaymentStatusAsync(string paymentIntentId);
        Task<PaymentResult> RequestRefundAsync(int bookingId, int userId, RefundRequest refundRequest);
    }

    public class PaymentRequest
    {
        public string BookingNumber { get; set; } = string.Empty;
        public string PaymentMethodId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public int UserId { get; set; }
        public int TripId { get; set; }
    }

    public class RefundRequest
    {
        public decimal Amount { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string Currency { get; set; } = "USD";
    }

    public class PaymentResult
    {
        public bool Success { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public PaymentStatus Status { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
    }

    public enum PaymentStatus
    {
        Pending,
        Processing,
        Success,
        Failed,
        Cancelled,
        Refunded,
        RequiresAction
    }
}
