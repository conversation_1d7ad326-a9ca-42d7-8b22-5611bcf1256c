namespace TravelTourism.Application.DTOs.Admin;

public class MonthlyStatsDto
{
    public int Month { get; set; }
    public string MonthName { get; set; } = string.Empty;
    public int Year { get; set; }
    public int TotalBookings { get; set; }
    public decimal TotalRevenue { get; set; }
    public int NewUsers { get; set; }
    public int NewTrips { get; set; }
    public int NewBlogs { get; set; }
    public decimal AverageBookingValue { get; set; }
    public int CancelledBookings { get; set; }
    public decimal CancellationRate { get; set; }
    public int ActiveUsers { get; set; }
    public int RepeatBookings { get; set; }
    public decimal CustomerSatisfactionScore { get; set; }
    public int NewBookings { get; set; }
    public decimal Revenue { get; set; }
    public int ConfirmedBookings { get; set; }
} 