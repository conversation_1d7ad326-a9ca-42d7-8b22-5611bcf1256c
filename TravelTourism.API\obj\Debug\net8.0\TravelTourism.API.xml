<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TravelTourism.API</name>
    </assembly>
    <members>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.Register(TravelTourism.Application.DTOs.Auth.RegisterDto)">
            <summary>
            Register a new user account
            </summary>
            <param name="registerDto">User registration information</param>
            <returns>Registration result with user details</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.Login(TravelTourism.Application.DTOs.Auth.LoginDto)">
            <summary>
            Login with email and password
            </summary>
            <param name="loginDto">Login credentials</param>
            <returns>Authentication result with JWT tokens</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.VerifyEmail(TravelTourism.Application.DTOs.Auth.VerifyEmailDto)">
            <summary>
            Verify email address
            </summary>
            <param name="verifyEmailDto">Email verification details</param>
            <returns>Verification result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.ForgotPassword(TravelTourism.Application.DTOs.Auth.ForgotPasswordDto)">
            <summary>
            Request password reset
            </summary>
            <param name="forgotPasswordDto">Forgot password request</param>
            <returns>Password reset result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.ResetPassword(TravelTourism.Application.DTOs.Auth.ResetPasswordDto)">
            <summary>
            Reset password with token
            </summary>
            <param name="resetPasswordDto">Password reset details</param>
            <returns>Password reset result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.RefreshToken(TravelTourism.Application.DTOs.Auth.RefreshTokenDto)">
            <summary>
            Refresh JWT access token
            </summary>
            <param name="refreshTokenDto">Refresh token</param>
            <returns>New access token</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.Logout">
            <summary>
            Logout current user (revoke all tokens)
            </summary>
            <returns>Logout result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.AuthController.RevokeAllTokens">
            <summary>
            Revoke all user tokens (force logout from all devices)
            </summary>
            <returns>Token revocation result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BlogsController.GetBlogs(System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
            <summary>
            Get all blogs with filtering and pagination
            </summary>
            <param name="page">Page number</param>
            <param name="pageSize">Items per page</param>
            <param name="search">Search term</param>
            <param name="category">Filter by category</param>
            <param name="sortBy">Sort field</param>
            <param name="sortDirection">Sort direction</param>
            <returns>Paginated list of blogs</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BlogsController.GetBlog(System.Int32)">
            <summary>
            Get blog by ID
            </summary>
            <param name="id">Blog ID</param>
            <returns>Blog details</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BlogsController.GetBlogCategories">
            <summary>
            Get all blog categories (public endpoint)
            </summary>
            <returns>List of blog categories</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BlogsController.GetFeaturedBlogs(System.Int32)">
            <summary>
            Get featured blogs
            </summary>
            <param name="count">Number of featured blogs to return</param>
            <returns>List of featured blogs</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BlogsController.GetBlogsByCategory(System.Int32,System.Int32,System.Int32)">
            <summary>
            Get blogs by category
            </summary>
            <param name="category">Category name</param>
            <param name="page">Page number</param>
            <param name="pageSize">Items per page</param>
            <returns>Paginated list of blogs in category</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BlogsController.GetRelatedBlogs(System.Int32,System.Int32)">
            <summary>
            Get related blogs
            </summary>
            <param name="id">Blog ID to find related blogs for</param>
            <param name="count">Number of related blogs to return</param>
            <returns>List of related blogs</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.CreateBooking(TravelTourism.Application.DTOs.Booking.CreateBookingRequest)">
            <summary>
            Create a new booking
            </summary>
            <param name="request">Booking creation request</param>
            <returns>Created booking details</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.GetBooking(System.Int32)">
            <summary>
            Get booking by ID
            </summary>
            <param name="id">Booking ID</param>
            <returns>Booking details</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.CancelBooking(System.Int32)">
            <summary>
            Cancel a booking
            </summary>
            <param name="id">Booking ID</param>
            <returns>Cancellation result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.ProcessPayment(System.Int32,TravelTourism.Core.Interfaces.Services.PaymentRequest)">
            <summary>
            Process payment for a booking
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Payment request</param>
            <returns>Payment result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.GetPaymentStatus(System.Int32,System.String)">
            <summary>
            Get payment status for a booking
            </summary>
            <param name="id">Booking ID</param>
            <param name="paymentIntentId">Payment Intent ID</param>
            <returns>Payment status</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.ConfirmPayment(System.Int32,TravelTourism.Application.DTOs.Booking.PaymentConfirmationRequest)">
            <summary>
            Confirm payment for a booking
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Payment confirmation request</param>
            <returns>Payment confirmation result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.RequestRefund(System.Int32,TravelTourism.Application.DTOs.Booking.RefundRequest)">
            <summary>
            Request refund for a booking
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Refund request</param>
            <returns>Refund result</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.GetInvoice(System.Int32)">
            <summary>
            Get booking invoice
            </summary>
            <param name="id">Booking ID</param>
            <returns>Invoice PDF</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.BookingsController.UpdateBooking(System.Int32,TravelTourism.Application.DTOs.Booking.UpdateBookingRequest)">
            <summary>
            Update booking details
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Update booking request</param>
            <returns>Updated booking details</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.GetTrips(TravelTourism.Application.DTOs.Trip.TripFilterDto,TravelTourism.Application.DTOs.Common.PaginationParameters)">
            <summary>
            Get paginated list of trips with optional filtering
            </summary>
            <param name="filter">Trip filter criteria</param>
            <param name="pagination">Pagination parameters</param>
            <returns>Paginated trips list</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.GetTrip(System.Int32)">
            <summary>
            Get trip details by ID
            </summary>
            <param name="id">Trip ID</param>
            <returns>Trip details</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.GetFeaturedTrips(System.Int32)">
            <summary>
            Get featured trips
            </summary>
            <param name="count">Number of featured trips to return (default: 10)</param>
            <returns>List of featured trips</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.GetCategories">
            <summary>
            Get all trip categories
            </summary>
            <returns>List of trip categories</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.GetDestinations">
            <summary>
            Get all available destinations
            </summary>
            <returns>List of destination cities</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.GetTripsByCategory(System.Int32)">
            <summary>
            Get trips by category
            </summary>
            <param name="categoryId">Category ID</param>
            <returns>List of trips in the category</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.GetTripsByDestination(System.Int32)">
            <summary>
            Get trips by destination
            </summary>
            <param name="destinationId">Destination city ID</param>
            <returns>List of trips to the destination</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.SearchTrips(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32})">
            <summary>
            Search trips
            </summary>
            <param name="searchTerm">Search term</param>
            <param name="categoryId">Optional category filter</param>
            <param name="destinationId">Optional destination filter</param>
            <returns>List of matching trips</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.TripsController.CheckAvailability(System.Int32,System.DateTime,System.Int32)">
            <summary>
            Check if a trip is available for booking
            </summary>
            <param name="tripId">Trip ID</param>
            <param name="travelDate">Travel date</param>
            <param name="numberOfPeople">Number of people</param>
            <returns>Availability status</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.UsersController.GetProfile">
            <summary>
            Get current user's profile
            </summary>
            <returns>User profile information</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.UsersController.UpdateProfile(TravelTourism.Application.DTOs.User.UpdateUserDto)">
            <summary>
            Update current user's profile
            </summary>
            <param name="updateUserDto">User update information</param>
            <returns>Updated user profile</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.UsersController.GetUserBookings(System.Nullable{TravelTourism.Core.Enums.BookingStatus},System.Int32,System.Int32)">
            <summary>
            Get current user's bookings
            </summary>
            <param name="status">Filter by booking status</param>
            <param name="page">Page number</param>
            <param name="pageSize">Items per page</param>
            <returns>List of user bookings</returns>
        </member>
        <member name="M:TravelTourism.API.Controllers.V1.UsersController.DeleteAccount">
            <summary>
            Delete current user's account
            </summary>
            <returns>Success response</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.GetBlogs(TravelTourism.Application.DTOs.Admin.AdminBlogFilterRequest)">
            <summary>
            Get all blogs with pagination and filtering
            </summary>
            <param name="request">Blogs filter and pagination request</param>
            <returns>Paginated list of blogs</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.GetBlog(System.Int32)">
            <summary>
            Get blog by ID
            </summary>
            <param name="id">Blog ID</param>
            <returns>Blog details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.CreateBlog(TravelTourism.Application.DTOs.Blog.CreateBlogRequest)">
            <summary>
            Create a new blog
            </summary>
            <param name="request">Blog creation request</param>
            <returns>Created blog details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.UpdateBlog(System.Int32,TravelTourism.Application.DTOs.Blog.UpdateBlogRequest)">
            <summary>
            Update blog details
            </summary>
            <param name="id">Blog ID</param>
            <param name="request">Update blog request</param>
            <returns>Updated blog details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.DeleteBlog(System.Int32)">
            <summary>
            Delete blog
            </summary>
            <param name="id">Blog ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.PublishBlog(System.Int32)">
            <summary>
            Publish blog
            </summary>
            <param name="id">Blog ID</param>
            <returns>Publication result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.UnpublishBlog(System.Int32)">
            <summary>
            Unpublish blog
            </summary>
            <param name="id">Blog ID</param>
            <returns>Unpublication result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.FeatureBlog(System.Int32)">
            <summary>
            Feature blog
            </summary>
            <param name="id">Blog ID</param>
            <returns>Feature result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.UnfeatureBlog(System.Int32)">
            <summary>
            Unfeature blog
            </summary>
            <param name="id">Blog ID</param>
            <returns>Unfeature result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.GetBlogStatistics(System.Int32)">
            <summary>
            Get blog statistics
            </summary>
            <param name="id">Blog ID</param>
            <returns>Blog statistics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.UploadBlogCoverImage(System.Int32,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Upload blog cover image
            </summary>
            <param name="id">Blog ID</param>
            <param name="file">Cover image file</param>
            <returns>Upload result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.DeleteBlogCoverImage(System.Int32)">
            <summary>
            Delete blog cover image
            </summary>
            <param name="id">Blog ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.GetBlogComments(System.Int32,TravelTourism.API.Models.Requests.PaginationRequest)">
            <summary>
            Get blog comments
            </summary>
            <param name="id">Blog ID</param>
            <param name="request">Pagination request</param>
            <returns>Blog comments</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.ApproveBlogComment(System.Int32,System.Int32)">
            <summary>
            Approve blog comment
            </summary>
            <param name="id">Blog ID</param>
            <param name="commentId">Comment ID</param>
            <returns>Approval result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.RejectBlogComment(System.Int32,System.Int32)">
            <summary>
            Reject blog comment
            </summary>
            <param name="id">Blog ID</param>
            <param name="commentId">Comment ID</param>
            <returns>Rejection result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.DeleteBlogComment(System.Int32,System.Int32)">
            <summary>
            Delete blog comment
            </summary>
            <param name="id">Blog ID</param>
            <param name="commentId">Comment ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.GetBlogCategories">
            <summary>
            Get blog categories
            </summary>
            <returns>List of blog categories</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.CreateBlogCategory(TravelTourism.Application.DTOs.Blog.CreateBlogCategoryRequest)">
            <summary>
            Create blog category
            </summary>
            <param name="request">Category creation request</param>
            <returns>Created category</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.UpdateBlogCategory(System.Int32,TravelTourism.Application.DTOs.Blog.UpdateBlogCategoryRequest)">
            <summary>
            Update blog category
            </summary>
            <param name="id">Category ID</param>
            <param name="request">Update category request</param>
            <returns>Updated category</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.DeleteBlogCategory(System.Int32)">
            <summary>
            Delete blog category
            </summary>
            <param name="id">Category ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.GetBlogTags">
            <summary>
            Get blog tags
            </summary>
            <returns>List of blog tags</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.CreateBlogTag(TravelTourism.Application.DTOs.Blog.CreateBlogTagRequest)">
            <summary>
            Create blog tag
            </summary>
            <param name="request">Tag creation request</param>
            <returns>Created tag</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.UpdateBlogTag(System.Int32,TravelTourism.Application.DTOs.Blog.UpdateBlogTagRequest)">
            <summary>
            Update blog tag
            </summary>
            <param name="id">Tag ID</param>
            <param name="request">Update tag request</param>
            <returns>Updated tag</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.DeleteBlogTag(System.Int32)">
            <summary>
            Delete blog tag
            </summary>
            <param name="id">Tag ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.BulkUpdateBlogs(TravelTourism.Application.DTOs.Admin.BulkUpdateBlogsRequest)">
            <summary>
            Bulk update blog status
            </summary>
            <param name="request">Bulk update request</param>
            <returns>Update result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBlogsController.ExportBlogs(TravelTourism.Application.DTOs.Admin.ExportBlogsRequest)">
            <summary>
            Export blogs to CSV
            </summary>
            <param name="request">Export request</param>
            <returns>CSV file</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookings(TravelTourism.Application.DTOs.Admin.AdminBookingFilterRequest)">
            <summary>
            Get all bookings with pagination and filtering
            </summary>
            <param name="request">Bookings filter and pagination request</param>
            <returns>Paginated list of bookings</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBooking(System.Int32)">
            <summary>
            Get booking by ID
            </summary>
            <param name="id">Booking ID</param>
            <returns>Booking details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.UpdateBooking(System.Int32,TravelTourism.Application.DTOs.Booking.UpdateBookingRequest)">
            <summary>
            Update booking details
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Update booking request</param>
            <returns>Updated booking details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.CancelBooking(System.Int32,TravelTourism.Application.DTOs.Admin.CancelBookingRequest)">
            <summary>
            Cancel booking
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Cancel booking request</param>
            <returns>Cancellation result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.DeleteBooking(System.Int32)">
            <summary>
            Delete booking
            </summary>
            <param name="id">Booking ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.ConfirmBooking(System.Int32)">
            <summary>
            Confirm booking
            </summary>
            <param name="id">Booking ID</param>
            <returns>Confirmation result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.ProcessRefund(System.Int32,TravelTourism.Application.DTOs.Admin.ProcessRefundRequest)">
            <summary>
            Process booking refund
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Refund request</param>
            <returns>Refund result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.UpdateBookingStatus(System.Int32,TravelTourism.Application.DTOs.Admin.UpdateBookingStatusRequest)">
            <summary>
            Update booking status
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Status update request</param>
            <returns>Update result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingStatistics">
            <summary>
            Get booking statistics
            </summary>
            <returns>Booking statistics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetRevenueStatistics(TravelTourism.Application.DTOs.Admin.RevenueStatisticsRequest)">
            <summary>
            Get booking revenue statistics
            </summary>
            <param name="request">Revenue statistics request</param>
            <returns>Revenue statistics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingAnalytics(TravelTourism.Application.DTOs.Admin.BookingAnalyticsRequest)">
            <summary>
            Get booking analytics
            </summary>
            <param name="request">Analytics request</param>
            <returns>Booking analytics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingReports(TravelTourism.Application.DTOs.Admin.BookingReportRequest)">
            <summary>
            Get booking reports
            </summary>
            <param name="request">Report request</param>
            <returns>Booking reports</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.ExportBookings(TravelTourism.Application.DTOs.Admin.ExportBookingsRequest)">
            <summary>
            Export bookings to CSV
            </summary>
            <param name="request">Export request</param>
            <returns>CSV file</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.ExportBookingInvoices(TravelTourism.Application.DTOs.Admin.ExportInvoicesRequest)">
            <summary>
            Export booking invoices to PDF
            </summary>
            <param name="request">Export request</param>
            <returns>PDF file</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.SendBookingReminder(System.Int32,TravelTourism.Application.DTOs.Admin.SendReminderRequest)">
            <summary>
            Send booking reminder
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Reminder request</param>
            <returns>Reminder result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingPayment(System.Int32)">
            <summary>
            Get booking payment details
            </summary>
            <param name="id">Booking ID</param>
            <returns>Payment details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingCustomer(System.Int32)">
            <summary>
            Get booking customer details
            </summary>
            <param name="id">Booking ID</param>
            <returns>Customer details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingTrip(System.Int32)">
            <summary>
            Get booking trip details
            </summary>
            <param name="id">Booking ID</param>
            <returns>Trip details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.AddBookingNote(System.Int32,TravelTourism.Application.DTOs.Admin.AddBookingNoteRequest)">
            <summary>
            Add booking note
            </summary>
            <param name="id">Booking ID</param>
            <param name="request">Note request</param>
            <returns>Note result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingNotes(System.Int32)">
            <summary>
            Get booking notes
            </summary>
            <param name="id">Booking ID</param>
            <returns>Booking notes</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.UpdateBookingNote(System.Int32,System.Int32,TravelTourism.Application.DTOs.Admin.UpdateBookingNoteRequest)">
            <summary>
            Update booking note
            </summary>
            <param name="id">Booking ID</param>
            <param name="noteId">Note ID</param>
            <param name="request">Update note request</param>
            <returns>Update result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.DeleteBookingNote(System.Int32,System.Int32)">
            <summary>
            Delete booking note
            </summary>
            <param name="id">Booking ID</param>
            <param name="noteId">Note ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.BulkUpdateBookings(TravelTourism.Application.DTOs.Admin.BulkUpdateBookingsRequest)">
            <summary>
            Bulk update bookings
            </summary>
            <param name="request">Bulk update request</param>
            <returns>Update result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminBookingsController.GetBookingActivityLog(System.Int32)">
            <summary>
            Get booking activity log
            </summary>
            <param name="id">Booking ID</param>
            <returns>Activity log</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetDashboardOverview">
            <summary>
            Get dashboard overview statistics
            </summary>
            <returns>Dashboard overview data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetSystemStatistics">
            <summary>
            Get system statistics
            </summary>
            <returns>System statistics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetRevenueAnalytics(TravelTourism.Application.DTOs.Admin.RevenueAnalyticsRequest)">
            <summary>
            Get revenue analytics
            </summary>
            <param name="request">Revenue analytics request</param>
            <returns>Revenue analytics data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetUserAnalytics(TravelTourism.Application.DTOs.Admin.UserAnalyticsRequest)">
            <summary>
            Get user analytics
            </summary>
            <param name="request">User analytics request</param>
            <returns>User analytics data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetBookingTrends(TravelTourism.Application.DTOs.Admin.BookingTrendsRequest)">
            <summary>
            Get booking trends
            </summary>
            <param name="request">Booking trends request</param>
            <returns>Booking trends data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetPopularDestinations(TravelTourism.Application.DTOs.Admin.PopularDestinationsRequest)">
            <summary>
            Get popular destinations
            </summary>
            <param name="request">Popular destinations request</param>
            <returns>Popular destinations data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetRecentActivities(TravelTourism.Application.DTOs.Admin.RecentActivitiesRequest)">
            <summary>
            Get recent activities
            </summary>
            <param name="request">Recent activities request</param>
            <returns>Recent activities data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetPerformanceMetrics(TravelTourism.Application.DTOs.Admin.PerformanceMetricsRequest)">
            <summary>
            Get performance metrics
            </summary>
            <param name="request">Performance metrics request</param>
            <returns>Performance metrics data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetSystemHealthStatus">
            <summary>
            Get system health status
            </summary>
            <returns>System health status</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetAuditLogs(TravelTourism.Application.DTOs.Admin.AuditLogsRequest)">
            <summary>
            Get audit logs
            </summary>
            <param name="request">Audit logs request</param>
            <returns>Audit logs data</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetSystemConfigurations">
            <summary>
            Get system configurations
            </summary>
            <returns>System configurations</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.UpdateSystemConfiguration(TravelTourism.Application.DTOs.Admin.UpdateConfigurationRequest)">
            <summary>
            Update system configuration
            </summary>
            <param name="request">Update configuration request</param>
            <returns>Update result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetDatabaseStatistics">
            <summary>
            Get database statistics
            </summary>
            <returns>Database statistics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetServerMetrics">
            <summary>
            Get server metrics
            </summary>
            <returns>Server metrics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetApplicationLogs(TravelTourism.Application.DTOs.Admin.ApplicationLogsRequest)">
            <summary>
            Get application logs
            </summary>
            <param name="request">Application logs request</param>
            <returns>Application logs</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetErrorLogs(TravelTourism.Application.DTOs.Admin.ErrorLogsRequest)">
            <summary>
            Get error logs
            </summary>
            <param name="request">Error logs request</param>
            <returns>Error logs</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.ClearApplicationCache">
            <summary>
            Clear application cache
            </summary>
            <returns>Clear cache result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.SendSystemNotification(TravelTourism.Application.DTOs.Admin.SendSystemNotificationRequest)">
            <summary>
            Send system notification
            </summary>
            <param name="request">System notification request</param>
            <returns>Notification result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.ExportSystemData(TravelTourism.Application.DTOs.Admin.ExportSystemDataRequest)">
            <summary>
            Export system data
            </summary>
            <param name="request">Export data request</param>
            <returns>Export result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetBackupStatus">
            <summary>
            Get backup status
            </summary>
            <returns>Backup status</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.CreateSystemBackup(TravelTourism.Application.DTOs.Admin.CreateBackupRequest)">
            <summary>
            Create system backup
            </summary>
            <param name="request">Backup request</param>
            <returns>Backup result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.GetMaintenanceModeStatus">
            <summary>
            Get maintenance mode status
            </summary>
            <returns>Maintenance mode status</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminDashboardController.ToggleMaintenanceMode(TravelTourism.Application.DTOs.Admin.ToggleMaintenanceModeRequest)">
            <summary>
            Toggle maintenance mode
            </summary>
            <param name="request">Toggle maintenance mode request</param>
            <returns>Toggle result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.GetUsers(TravelTourism.Application.DTOs.Admin.GetUsersRequest)">
            <summary>
            Get all users with pagination and filtering
            </summary>
            <param name="request">Users filter and pagination request</param>
            <returns>Paginated list of users</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.GetUser(System.Int32)">
            <summary>
            Get user by ID
            </summary>
            <param name="id">User ID</param>
            <returns>User details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.CreateUser(TravelTourism.Application.DTOs.Admin.CreateUserRequest)">
            <summary>
            Create a new user
            </summary>
            <param name="request">User creation request</param>
            <returns>Created user details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.UpdateUser(System.Int32,TravelTourism.Application.DTOs.Admin.UpdateUserRequest)">
            <summary>
            Update user details
            </summary>
            <param name="id">User ID</param>
            <param name="request">Update user request</param>
            <returns>Updated user details</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.DeleteUser(System.Int32)">
            <summary>
            Delete user
            </summary>
            <param name="id">User ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.ActivateUser(System.Int32)">
            <summary>
            Activate user account
            </summary>
            <param name="id">User ID</param>
            <returns>Activation result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.DeactivateUser(System.Int32)">
            <summary>
            Deactivate user account
            </summary>
            <param name="id">User ID</param>
            <returns>Deactivation result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.AssignRole(System.Int32,TravelTourism.Application.DTOs.Admin.AssignRoleRequest)">
            <summary>
            Assign role to user
            </summary>
            <param name="id">User ID</param>
            <param name="request">Role assignment request</param>
            <returns>Role assignment result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.RemoveRole(System.Int32,System.String)">
            <summary>
            Remove role from user
            </summary>
            <param name="id">User ID</param>
            <param name="roleName">Role name</param>
            <returns>Role removal result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.GetUserBookings(System.Int32,TravelTourism.API.Models.Requests.PaginationRequest)">
            <summary>
            Get user's bookings
            </summary>
            <param name="id">User ID</param>
            <param name="request">Pagination request</param>
            <returns>User's bookings</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.GetUserStatistics(System.Int32)">
            <summary>
            Get user statistics
            </summary>
            <param name="id">User ID</param>
            <returns>User statistics</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.SendNotification(System.Int32,TravelTourism.Application.DTOs.Admin.SendNotificationRequest)">
            <summary>
            Send notification to user
            </summary>
            <param name="id">User ID</param>
            <param name="request">Notification request</param>
            <returns>Notification result</returns>
        </member>
        <member name="M:TravelTourism.Controllers.Admin.AdminUsersController.ResetPassword(System.Int32)">
            <summary>
            Reset user password
            </summary>
            <param name="id">User ID</param>
            <returns>Password reset result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.GetTrips(TravelTourism.Application.DTOs.Admin.AdminTripFilterRequest)">
            <summary>
            Get all trips with pagination and filtering
            </summary>
            <param name="request">Trips filter and pagination request</param>
            <returns>Paginated list of trips</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.GetTrip(System.Int32)">
            <summary>
            Get trip by ID
            </summary>
            <param name="id">Trip ID</param>
            <returns>Trip details</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.CreateTrip(TravelTourism.Application.DTOs.Trip.CreateTripRequest)">
            <summary>
            Create a new trip
            </summary>
            <param name="request">Trip creation request</param>
            <returns>Created trip details</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.UpdateTrip(System.Int32,TravelTourism.Application.DTOs.Trip.UpdateTripRequest)">
            <summary>
            Update trip details
            </summary>
            <param name="id">Trip ID</param>
            <param name="request">Update trip request</param>
            <returns>Updated trip details</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.DeleteTrip(System.Int32)">
            <summary>
            Delete trip
            </summary>
            <param name="id">Trip ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.PublishTrip(System.Int32)">
            <summary>
            Publish trip
            </summary>
            <param name="id">Trip ID</param>
            <returns>Publication result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.UnpublishTrip(System.Int32)">
            <summary>
            Unpublish trip
            </summary>
            <param name="id">Trip ID</param>
            <returns>Unpublication result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.FeatureTrip(System.Int32)">
            <summary>
            Feature trip
            </summary>
            <param name="id">Trip ID</param>
            <returns>Feature result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.UnfeatureTrip(System.Int32)">
            <summary>
            Unfeature trip
            </summary>
            <param name="id">Trip ID</param>
            <returns>Unfeature result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.GetTripBookings(System.Int32,TravelTourism.API.Models.Requests.PaginationRequest)">
            <summary>
            Get trip bookings
            </summary>
            <param name="id">Trip ID</param>
            <param name="request">Pagination request</param>
            <returns>Trip bookings</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.GetTripStatistics(System.Int32)">
            <summary>
            Get trip statistics
            </summary>
            <param name="id">Trip ID</param>
            <returns>Trip statistics</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.UploadTripImages(System.Int32,System.Collections.Generic.IList{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            Upload trip images
            </summary>
            <param name="id">Trip ID</param>
            <param name="files">Image files</param>
            <returns>Upload result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.DeleteTripImage(System.Int32,System.Int32)">
            <summary>
            Delete trip image
            </summary>
            <param name="id">Trip ID</param>
            <param name="imageId">Image ID</param>
            <returns>Deletion result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.DuplicateTrip(System.Int32)">
            <summary>
            Duplicate trip
            </summary>
            <param name="id">Trip ID</param>
            <returns>Duplicated trip</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.GetTripReviews(System.Int32,TravelTourism.API.Models.Requests.PaginationRequest)">
            <summary>
            Get trip reviews
            </summary>
            <param name="id">Trip ID</param>
            <param name="request">Pagination request</param>
            <returns>Trip reviews</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.BulkUpdateTrips(TravelTourism.Application.DTOs.Admin.BulkUpdateTripsRequest)">
            <summary>
            Bulk update trip status
            </summary>
            <param name="request">Bulk update request</param>
            <returns>Update result</returns>
        </member>
        <member name="M:TravelTourismAPI.Controllers.Admin.AdminTripsController.ExportTrips(TravelTourism.Application.DTOs.Admin.ExportTripsRequest)">
            <summary>
            Export trips to CSV
            </summary>
            <param name="request">Export request</param>
            <returns>CSV file</returns>
        </member>
    </members>
</doc>
