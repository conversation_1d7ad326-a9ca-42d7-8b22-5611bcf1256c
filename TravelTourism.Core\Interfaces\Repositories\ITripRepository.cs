using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TravelTourism.Core.Entities.Trip;

namespace TravelTourism.Core.Interfaces.Repositories
{
    public interface ITripRepository : IGenericRepository<Trip>
    {
        IQueryable<Trip> GetQueryable();
        Task<IReadOnlyList<Trip>> GetFeaturedTripsAsync(int count = 10);
        Task<IReadOnlyList<Trip>> GetTripsByCategoryAsync(int categoryId);
        Task<IReadOnlyList<Trip>> GetTripsByDestinationAsync(int cityId);
        Task<Trip> GetTripWithDetailsAsync(int id);
        Task<IReadOnlyList<TripCategory>> GetCategoriesAsync();
        Task<bool> IsTripAvailableAsync(int tripId, int numberOfPeople);
    }
}
