using System.Threading.Tasks;

namespace TravelTourism.Core.Interfaces.Services
{
    public interface IEmailService
    {
        Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
        Task SendEmailVerificationAsync(string to, string verificationLink);
        Task SendPasswordResetAsync(string to, string resetLink);
        Task SendBookingConfirmationAsync(string to, string bookingNumber, string tripName);
        Task SendWelcomeEmailAsync(string to, string firstName);
    }
}
