using System.ComponentModel.DataAnnotations;

namespace TravelTourism.Application.DTOs.Common;

public class CountryDto
{
    public int Id { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(5)]
    public string Code { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string Currency { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(10)]
    public string CurrencyCode { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(5)]
    public string CurrencySymbol { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? FlagUrl { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public int CityCount { get; set; }
} 