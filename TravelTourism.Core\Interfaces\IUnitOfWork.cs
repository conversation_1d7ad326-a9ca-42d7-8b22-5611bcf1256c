using System;
using System.Threading.Tasks;
using TravelTourism.Core.Interfaces.Repositories;

namespace TravelTourism.Core.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        IUserRepository Users { get; }
        ITripRepository Trips { get; }
        IBlogRepository Blogs { get; }
        IBookingRepository Bookings { get; }
        ICountryRepository Countries { get; }
        ICityRepository Cities { get; }

        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
