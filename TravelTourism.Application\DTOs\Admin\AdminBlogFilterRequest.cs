namespace TravelTourism.Application.DTOs.Admin
{
    public class AdminBlogFilterRequest
    {
        public string? SearchTerm { get; set; }
        public int? CategoryId { get; set; }
        public string? Author { get; set; }
        public bool? IsPublished { get; set; }
        public DateTime? PublishedDateFrom { get; set; }
        public DateTime? PublishedDateTo { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = false;
    }
} 