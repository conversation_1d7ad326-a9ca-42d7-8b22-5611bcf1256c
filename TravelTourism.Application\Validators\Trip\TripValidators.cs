using FluentValidation;
using TravelTourism.Application.DTOs.Trip;
using TravelTourism.Core.Constants;

namespace TravelTourism.Application.Validators.Trip
{
    public class CreateTripValidator : AbstractValidator<CreateTripDto>
    {
        public CreateTripValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Trip name is required")
                .MaximumLength(200).WithMessage("Trip name cannot exceed 200 characters");

            RuleFor(x => x.Description)
                .NotEmpty().WithMessage("Description is required");

            RuleFor(x => x.ShortDescription)
                .NotEmpty().WithMessage("Short description is required")
                .MaximumLength(500).WithMessage("Short description cannot exceed 500 characters");

            RuleFor(x => x.Price)
                .GreaterThan(0).WithMessage("Price must be greater than 0");

            RuleFor(x => x.DiscountPrice)
                .LessThan(x => x.Price).WithMessage("Discount price must be less than regular price")
                .GreaterThan(0).WithMessage("Discount price must be greater than 0")
                .When(x => x.DiscountPrice.HasValue);

            RuleFor(x => x.Duration)
                .GreaterThanOrEqualTo(DomainConstants.Trip.MinDuration)
                .WithMessage($"Duration must be at least {DomainConstants.Trip.MinDuration} day(s)")
                .LessThanOrEqualTo(DomainConstants.Trip.MaxDuration)
                .WithMessage($"Duration cannot exceed {DomainConstants.Trip.MaxDuration} days");

            RuleFor(x => x.MaxCapacity)
                .GreaterThanOrEqualTo(DomainConstants.Trip.MinCapacity)
                .WithMessage($"Max capacity must be at least {DomainConstants.Trip.MinCapacity}")
                .LessThanOrEqualTo(DomainConstants.Trip.MaxCapacity)
                .WithMessage($"Max capacity cannot exceed {DomainConstants.Trip.MaxCapacity}");

            RuleFor(x => x.MinAge)
                .GreaterThanOrEqualTo(0).WithMessage("Minimum age cannot be negative")
                .LessThan(x => x.MaxAge).WithMessage("Minimum age must be less than maximum age")
                .When(x => x.MinAge.HasValue && x.MaxAge.HasValue);

            RuleFor(x => x.MaxAge)
                .GreaterThan(0).WithMessage("Maximum age must be greater than 0")
                .LessThanOrEqualTo(120).WithMessage("Maximum age cannot exceed 120")
                .When(x => x.MaxAge.HasValue);

            RuleFor(x => x.CategoryId)
                .GreaterThan(0).WithMessage("Category is required");

            RuleFor(x => x.DestinationCityId)
                .GreaterThan(0).WithMessage("Destination city is required");

            RuleFor(x => x.DepartureCityId)
                .GreaterThan(0).WithMessage("Departure city is required");

            RuleFor(x => x.AvailableFrom)
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("Available from date cannot be in the past");

            RuleFor(x => x.AvailableTo)
                .GreaterThan(x => x.AvailableFrom).WithMessage("Available to date must be after available from date");

            RuleFor(x => x.MainImageUrl)
                .NotEmpty().WithMessage("Main image is required")
                .MaximumLength(500).WithMessage("Image URL cannot exceed 500 characters");

            RuleForEach(x => x.Itineraries)
                .SetValidator(new TripItineraryValidator());
        }
    }

    public class UpdateTripValidator : AbstractValidator<UpdateTripDto>
    {
        public UpdateTripValidator()
        {
            RuleFor(x => x.Id)
                .GreaterThan(0).WithMessage("Trip ID is required");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Trip name is required")
                .MaximumLength(200).WithMessage("Trip name cannot exceed 200 characters");

            RuleFor(x => x.Description)
                .NotEmpty().WithMessage("Description is required");

            RuleFor(x => x.ShortDescription)
                .NotEmpty().WithMessage("Short description is required")
                .MaximumLength(500).WithMessage("Short description cannot exceed 500 characters");

            RuleFor(x => x.Price)
                .GreaterThan(0).WithMessage("Price must be greater than 0");

            RuleFor(x => x.DiscountPrice)
                .LessThan(x => x.Price).WithMessage("Discount price must be less than regular price")
                .GreaterThan(0).WithMessage("Discount price must be greater than 0")
                .When(x => x.DiscountPrice.HasValue);

            RuleFor(x => x.Duration)
                .GreaterThanOrEqualTo(DomainConstants.Trip.MinDuration)
                .WithMessage($"Duration must be at least {DomainConstants.Trip.MinDuration} day(s)")
                .LessThanOrEqualTo(DomainConstants.Trip.MaxDuration)
                .WithMessage($"Duration cannot exceed {DomainConstants.Trip.MaxDuration} days");

            RuleFor(x => x.MaxCapacity)
                .GreaterThanOrEqualTo(DomainConstants.Trip.MinCapacity)
                .WithMessage($"Max capacity must be at least {DomainConstants.Trip.MinCapacity}")
                .LessThanOrEqualTo(DomainConstants.Trip.MaxCapacity)
                .WithMessage($"Max capacity cannot exceed {DomainConstants.Trip.MaxCapacity}");

            RuleFor(x => x.MinAge)
                .GreaterThanOrEqualTo(0).WithMessage("Minimum age cannot be negative")
                .LessThan(x => x.MaxAge).WithMessage("Minimum age must be less than maximum age")
                .When(x => x.MinAge.HasValue && x.MaxAge.HasValue);

            RuleFor(x => x.MaxAge)
                .GreaterThan(0).WithMessage("Maximum age must be greater than 0")
                .LessThanOrEqualTo(120).WithMessage("Maximum age cannot exceed 120")
                .When(x => x.MaxAge.HasValue);

            RuleFor(x => x.CategoryId)
                .GreaterThan(0).WithMessage("Category is required");

            RuleFor(x => x.DestinationCityId)
                .GreaterThan(0).WithMessage("Destination city is required");

            RuleFor(x => x.DepartureCityId)
                .GreaterThan(0).WithMessage("Departure city is required");

            RuleFor(x => x.AvailableFrom)
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("Available from date cannot be in the past");

            RuleFor(x => x.AvailableTo)
                .GreaterThan(x => x.AvailableFrom).WithMessage("Available to date must be after available from date");

            RuleFor(x => x.MainImageUrl)
                .NotEmpty().WithMessage("Main image is required")
                .MaximumLength(500).WithMessage("Image URL cannot exceed 500 characters");

            RuleForEach(x => x.Itineraries)
                .SetValidator(new TripItineraryValidator());
        }
    }

    public class CreateTripItineraryValidator : AbstractValidator<CreateTripItineraryDto>
    {
        public CreateTripItineraryValidator()
        {
            RuleFor(x => x.Day)
                .GreaterThan(0).WithMessage("Day must be greater than 0");

            RuleFor(x => x.Title)
                .NotEmpty().WithMessage("Itinerary title is required")
                .MaximumLength(200).WithMessage("Title cannot exceed 200 characters");

            RuleFor(x => x.Description)
                .NotEmpty().WithMessage("Itinerary description is required")
                .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters");

            RuleFor(x => x.Activities)
                .MaximumLength(1000).WithMessage("Activities cannot exceed 1000 characters")
                .When(x => !string.IsNullOrEmpty(x.Activities));

            RuleFor(x => x.Meals)
                .MaximumLength(200).WithMessage("Meals cannot exceed 200 characters")
                .When(x => !string.IsNullOrEmpty(x.Meals));

            RuleFor(x => x.Accommodation)
                .MaximumLength(200).WithMessage("Accommodation cannot exceed 200 characters")
                .When(x => !string.IsNullOrEmpty(x.Accommodation));
        }
    }

    public class TripItineraryValidator : AbstractValidator<TripItineraryDto>
    {
        public TripItineraryValidator()
        {
            RuleFor(x => x.Day)
                .GreaterThan(0).WithMessage("Day must be greater than 0");

            RuleFor(x => x.Title)
                .NotEmpty().WithMessage("Itinerary title is required")
                .MaximumLength(200).WithMessage("Title cannot exceed 200 characters");

            RuleFor(x => x.Description)
                .NotEmpty().WithMessage("Itinerary description is required")
                .MaximumLength(1000).WithMessage("Description cannot exceed 1000 characters");

            RuleFor(x => x.Activities)
                .MaximumLength(1000).WithMessage("Activities cannot exceed 1000 characters")
                .When(x => !string.IsNullOrEmpty(x.Activities));

            RuleFor(x => x.Meals)
                .MaximumLength(200).WithMessage("Meals cannot exceed 200 characters")
                .When(x => !string.IsNullOrEmpty(x.Meals));

            RuleFor(x => x.Accommodation)
                .MaximumLength(200).WithMessage("Accommodation cannot exceed 200 characters")
                .When(x => !string.IsNullOrEmpty(x.Accommodation));
        }
    }

    public class TripFilterValidator : AbstractValidator<TripFilterDto>
    {
        public TripFilterValidator()
        {
            RuleFor(x => x.MinPrice)
                .GreaterThanOrEqualTo(0).WithMessage("Minimum price cannot be negative")
                .When(x => x.MinPrice.HasValue);

            RuleFor(x => x.MaxPrice)
                .GreaterThan(x => x.MinPrice).WithMessage("Maximum price must be greater than minimum price")
                .When(x => x.MaxPrice.HasValue && x.MinPrice.HasValue);

            RuleFor(x => x.MinDuration)
                .GreaterThan(0).WithMessage("Minimum duration must be greater than 0")
                .When(x => x.MinDuration.HasValue);

            RuleFor(x => x.MaxDuration)
                .GreaterThan(x => x.MinDuration).WithMessage("Maximum duration must be greater than minimum duration")
                .When(x => x.MaxDuration.HasValue && x.MinDuration.HasValue);

            RuleFor(x => x.AvailableTo)
                .GreaterThan(x => x.AvailableFrom).WithMessage("Available to date must be after available from date")
                .When(x => x.AvailableTo.HasValue && x.AvailableFrom.HasValue);

            RuleFor(x => x.SortBy)
                .Must(x => string.IsNullOrEmpty(x) || new[] { "Name", "Price", "Duration", "CreatedAt", "AvailableFrom" }.Contains(x))
                .WithMessage("Invalid sort field");

            RuleFor(x => x.SortDirection)
                .Must(x => string.IsNullOrEmpty(x) || new[] { "Asc", "Desc" }.Contains(x))
                .WithMessage("Sort direction must be 'Asc' or 'Desc'");
        }
    }
}
