using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;
using TravelTourism.Core.Entities.Trip;

namespace TravelTourism.Core.Entities.Common
{
    public class City : BaseEntity
    {
        public string Name { get; set; }
        public int CountryId { get; set; }
        public string StateProvince { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string TimeZone { get; set; }
        public bool IsPopular { get; set; } = false;
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Country Country { get; set; }
        public virtual ICollection<Trip.Trip> DestinationTrips { get; set; } = new List<Trip.Trip>();
        public virtual ICollection<Trip.Trip> DepartureTrips { get; set; } = new List<Trip.Trip>();
    }
}
