using System;

namespace TravelTourism.Core.ValueObjects
{
    public class Address
    {
        public string AddressLine1 { get; private set; }
        public string AddressLine2 { get; private set; }
        public string City { get; private set; }
        public string State { get; private set; }
        public string Country { get; private set; }
        public string PostalCode { get; private set; }

        public Address(string addressLine1, string city, string state, string country, string postalCode, string addressLine2 = null)
        {
            if (string.IsNullOrWhiteSpace(addressLine1))
                throw new ArgumentException("Address line 1 cannot be empty", nameof(addressLine1));

            if (string.IsNullOrWhiteSpace(city))
                throw new ArgumentException("City cannot be empty", nameof(city));

            if (string.IsNullOrWhiteSpace(country))
                throw new ArgumentException("Country cannot be empty", nameof(country));

            AddressLine1 = addressLine1;
            AddressLine2 = addressLine2;
            City = city;
            State = state;
            Country = country;
            PostalCode = postalCode;
        }

        public string GetFullAddress()
        {
            var parts = new[] { AddressLine1, AddressLine2, City, State, Country, PostalCode };
            return string.Join(", ", Array.FindAll(parts, p => !string.IsNullOrWhiteSpace(p)));
        }

        public override string ToString() => GetFullAddress();

        public override bool Equals(object obj)
        {
            return obj is Address address &&
                   AddressLine1 == address.AddressLine1 &&
                   AddressLine2 == address.AddressLine2 &&
                   City == address.City &&
                   State == address.State &&
                   Country == address.Country &&
                   PostalCode == address.PostalCode;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(AddressLine1, AddressLine2, City, State, Country, PostalCode);
        }
    }
}
