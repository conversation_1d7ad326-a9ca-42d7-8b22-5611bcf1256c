using AutoMapper;
using Microsoft.Extensions.Configuration;
using TravelTourism.Application.Common;
using TravelTourism.Application.DTOs.Auth;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;
using TravelTourism.Core.Constants;
using TravelTourism.Core.Entities.User;
using TravelTourism.Core.Enums;
using TravelTourism.Core.Interfaces;
using TravelTourism.Core.Interfaces.Services;
using BCrypt.Net;

namespace TravelTourism.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly IEmailService _emailService;

        public AuthService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IConfiguration configuration,
            IEmailService emailService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _configuration = configuration;
            _emailService = emailService;
        }

        public async Task<AuthResultDto> RegisterAsync(RegisterDto registerDto)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _unitOfWork.Users.GetByEmailAsync(registerDto.Email);
                if (existingUser != null)
                {
                    return new AuthResultDto
                    {
                        Success = false,
                        Message = "User with this email already exists",
                        Errors = new List<string> { "Email is already registered" }
                    };
                }

                // Map DTO to entity
                var user = _mapper.Map<User>(registerDto);
                
                // Hash password
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(registerDto.Password);
                
                // Generate email verification token
                user.EmailVerificationToken = JwtHelper.GenerateRandomToken();
                user.EmailVerificationTokenExpiry = DateTime.UtcNow.AddHours(DomainConstants.Email.VerificationTokenExpirationHours);
                
                // Create user
                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // Send verification email
                var verificationLink = GenerateEmailVerificationLink(user.Email, user.EmailVerificationToken);
                await _emailService.SendEmailVerificationAsync(user.Email, verificationLink);

                var userDto = _mapper.Map<UserDto>(user);

                return new AuthResultDto
                {
                    Success = true,
                    Message = "Registration successful. Please check your email to verify your account.",
                    User = userDto
                };
            }
            catch (Exception ex)
            {
                return new AuthResultDto
                {
                    Success = false,
                    Message = "Registration failed",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<AuthResultDto> LoginAsync(LoginDto loginDto)
        {
            try
            {
                // Find user by email
                var user = await _unitOfWork.Users.GetByEmailAsync(loginDto.Email);
                if (user == null)
                {
                    return new AuthResultDto
                    {
                        Success = false,
                        Message = "Invalid email or password",
                        Errors = new List<string> { "Invalid credentials" }
                    };
                }

                // Check if user is active
                if (!user.IsActive)
                {
                    return new AuthResultDto
                    {
                        Success = false,
                        Message = "Account is deactivated",
                        Errors = new List<string> { "Account is not active" }
                    };
                }

                // Verify password
                if (!BCrypt.Net.BCrypt.Verify(loginDto.Password, user.PasswordHash))
                {
                    return new AuthResultDto
                    {
                        Success = false,
                        Message = "Invalid email or password",
                        Errors = new List<string> { "Invalid credentials" }
                    };
                }

                // Check if email is verified
                if (!user.IsEmailVerified)
                {
                    return new AuthResultDto
                    {
                        Success = false,
                        Message = "Please verify your email before logging in",
                        Errors = new List<string> { "Email not verified" }
                    };
                }

                // Generate tokens
                var jwtSettings = _configuration.GetSection("JwtSettings");
                var accessToken = JwtHelper.GenerateJwtToken(
                    user,
                    jwtSettings["Key"],
                    jwtSettings["Issuer"],
                    jwtSettings["Audience"],
                    int.Parse(jwtSettings["DurationInMinutes"])
                );

                var refreshToken = JwtHelper.GenerateRefreshToken();
                var refreshTokenExpiry = DateTime.UtcNow.AddDays(7); // 7 days

                // Store refresh token
                var userToken = new UserToken
                {
                    UserId = user.Id,
                    RefreshToken = refreshToken,
                    ExpiryDate = refreshTokenExpiry,
                    IsActive = true
                };

                // Note: UserToken should be added through a separate repository or context
                // For now, we'll just update the user

                // Update last login
                user.UpdateLastLogin();
                _unitOfWork.Users.Update(user);

                await _unitOfWork.SaveChangesAsync();

                var userDto = _mapper.Map<UserDto>(user);
                var tokenDto = new TokenDto
                {
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(int.Parse(jwtSettings["DurationInMinutes"]))
                };

                return new AuthResultDto
                {
                    Success = true,
                    Message = "Login successful",
                    User = userDto,
                    Token = tokenDto
                };
            }
            catch (Exception ex)
            {
                return new AuthResultDto
                {
                    Success = false,
                    Message = "Login failed",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> VerifyEmailAsync(VerifyEmailDto verifyEmailDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByEmailVerificationTokenAsync(verifyEmailDto.Token);
                if (user == null || user.Email != verifyEmailDto.Email)
                {
                    return ApiResponse.ErrorResponse("Invalid verification token");
                }

                if (!user.IsEmailVerificationTokenValid(verifyEmailDto.Token))
                {
                    return ApiResponse.ErrorResponse("Verification token has expired");
                }

                user.VerifyEmail();
                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Send welcome email
                await _emailService.SendWelcomeEmailAsync(user.Email, user.FirstName);

                return ApiResponse.SuccessResponse("Email verified successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse("Email verification failed", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse> ForgotPasswordAsync(ForgotPasswordDto forgotPasswordDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByEmailAsync(forgotPasswordDto.Email);
                if (user == null)
                {
                    // Don't reveal that user doesn't exist
                    return ApiResponse.SuccessResponse("If the email exists, a password reset link has been sent");
                }

                // Generate reset token
                user.PasswordResetToken = JwtHelper.GenerateRandomToken();
                user.PasswordResetTokenExpiry = DateTime.UtcNow.AddHours(DomainConstants.Email.PasswordResetTokenExpirationHours);

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Send reset email
                var resetLink = GeneratePasswordResetLink(user.Email, user.PasswordResetToken);
                await _emailService.SendPasswordResetAsync(user.Email, resetLink);

                return ApiResponse.SuccessResponse("Password reset link has been sent to your email");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse("Password reset failed", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse> ResetPasswordAsync(ResetPasswordDto resetPasswordDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByPasswordResetTokenAsync(resetPasswordDto.Token);
                if (user == null || user.Email != resetPasswordDto.Email)
                {
                    return ApiResponse.ErrorResponse("Invalid reset token");
                }

                if (!user.IsPasswordResetTokenValid(resetPasswordDto.Token))
                {
                    return ApiResponse.ErrorResponse("Reset token has expired");
                }

                // Update password
                user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(resetPasswordDto.NewPassword);
                user.PasswordResetToken = null;
                user.PasswordResetTokenExpiry = null;
                user.UpdatedAt = DateTime.UtcNow;

                // Revoke all existing tokens
                await _unitOfWork.Users.RevokeAllUserTokensAsync(user.Id);

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                return ApiResponse.SuccessResponse("Password reset successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse("Password reset failed", new List<string> { ex.Message });
            }
        }

        public async Task<AuthResultDto> RefreshTokenAsync(RefreshTokenDto refreshTokenDto)
        {
            try
            {
                var userToken = await _unitOfWork.Users.GetRefreshTokenAsync(refreshTokenDto.RefreshToken);
                if (userToken == null || !userToken.IsActive || userToken.IsExpired)
                {
                    return new AuthResultDto
                    {
                        Success = false,
                        Message = "Invalid refresh token",
                        Errors = new List<string> { "Token is invalid or expired" }
                    };
                }

                var user = await _unitOfWork.Users.GetByIdAsync(userToken.UserId);
                if (user == null || !user.IsActive)
                {
                    return new AuthResultDto
                    {
                        Success = false,
                        Message = "User not found or inactive",
                        Errors = new List<string> { "Invalid user" }
                    };
                }

                // Generate new access token
                var jwtSettings = _configuration.GetSection("JwtSettings");
                var accessToken = JwtHelper.GenerateJwtToken(
                    user,
                    jwtSettings["Key"],
                    jwtSettings["Issuer"],
                    jwtSettings["Audience"],
                    int.Parse(jwtSettings["DurationInMinutes"])
                );

                var userDto = _mapper.Map<UserDto>(user);
                var tokenDto = new TokenDto
                {
                    AccessToken = accessToken,
                    RefreshToken = refreshTokenDto.RefreshToken,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(int.Parse(jwtSettings["DurationInMinutes"]))
                };

                return new AuthResultDto
                {
                    Success = true,
                    Message = "Token refreshed successfully",
                    User = userDto,
                    Token = tokenDto
                };
            }
            catch (Exception ex)
            {
                return new AuthResultDto
                {
                    Success = false,
                    Message = "Token refresh failed",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        public async Task<ApiResponse> LogoutAsync(int userId)
        {
            try
            {
                await _unitOfWork.Users.RevokeAllUserTokensAsync(userId);
                await _unitOfWork.SaveChangesAsync();

                return ApiResponse.SuccessResponse("Logged out successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse("Logout failed", new List<string> { ex.Message });
            }
        }

        public async Task<ApiResponse> RevokeAllUserTokensAsync(int userId)
        {
            try
            {
                await _unitOfWork.Users.RevokeAllUserTokensAsync(userId);
                await _unitOfWork.SaveChangesAsync();

                return ApiResponse.SuccessResponse("All tokens revoked successfully");
            }
            catch (Exception ex)
            {
                return ApiResponse.ErrorResponse("Token revocation failed", new List<string> { ex.Message });
            }
        }

        private string GenerateEmailVerificationLink(string email, string token)
        {
            var baseUrl = _configuration["AppSettings:BaseUrl"] ?? "https://localhost:5001";
            return $"{baseUrl}/api/v1/auth/verify-email?email={Uri.EscapeDataString(email)}&token={Uri.EscapeDataString(token)}";
        }

        private string GeneratePasswordResetLink(string email, string token)
        {
            var baseUrl = _configuration["AppSettings:BaseUrl"] ?? "https://localhost:5001";
            return $"{baseUrl}/reset-password?email={Uri.EscapeDataString(email)}&token={Uri.EscapeDataString(token)}";
        }
    }
}
