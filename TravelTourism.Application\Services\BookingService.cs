using AutoMapper;
using Microsoft.Extensions.Logging;
using TravelTourism.Application.DTOs.Booking;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;
using TravelTourism.Core.Entities.Booking;
using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Interfaces;
using TravelTourism.Core.Interfaces.Services;
using TravelTourism.Core.Enums;

namespace TravelTourism.Application.Services
{
    public class BookingService : IBookingService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<BookingService> _logger;
        private readonly IPaymentService _paymentService;
        private readonly IEmailService _emailService;
        private readonly ICacheService _cacheService;

        public BookingService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<BookingService> logger,
            IPaymentService paymentService,
            IEmailService emailService,
            ICacheService cacheService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _paymentService = paymentService;
            _emailService = emailService;
            _cacheService = cacheService;
        }

        public async Task<PagedResult<BookingDto>> GetBookingsAsync(PaginationParameters parameters)
        {
            try
            {
                var cacheKey = $"bookings:{parameters.PageNumber}:{parameters.PageSize}";
                var cachedResult = await _cacheService.GetAsync<PagedResult<BookingDto>>(cacheKey);
                
                if (cachedResult != null)
                {
                    return cachedResult;
                }

                var bookings = await _unitOfWork.Bookings.GetAllAsync();

                // Apply pagination
                var totalCount = bookings.Count;
                var pagedBookings = bookings
                    .Skip(parameters.Skip)
                    .Take(parameters.Take)
                    .ToList();

                var bookingDtos = _mapper.Map<List<BookingDto>>(pagedBookings);
                var result = PagedResult<BookingDto>.Create(bookingDtos, totalCount, parameters.PageNumber, parameters.PageSize);

                // Cache for 5 minutes
                await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(5));

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving bookings");
                throw;
            }
        }

        public async Task<BookingDto?> GetBookingByIdAsync(int id)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(id);
                if (booking == null)
                {
                    return null;
                }

                return _mapper.Map<BookingDto>(booking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving booking with ID {BookingId}", id);
                throw;
            }
        }

        public async Task<BookingDto?> GetBookingByNumberAsync(string bookingNumber)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByBookingNumberAsync(bookingNumber);
                if (booking == null)
                {
                    return null;
                }

                return _mapper.Map<BookingDto>(booking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving booking with number {BookingNumber}", bookingNumber);
                throw;
            }
        }

        public async Task<PagedResult<BookingDto>> GetUserBookingsAsync(int userId, PaginationParameters parameters)
        {
            try
            {
                var cacheKey = $"user_bookings:{userId}:{parameters.PageNumber}:{parameters.PageSize}";
                var cachedResult = await _cacheService.GetAsync<PagedResult<BookingDto>>(cacheKey);
                
                if (cachedResult != null)
                {
                    return cachedResult;
                }

                var bookings = await _unitOfWork.Bookings.GetUserBookingsAsync(userId);

                // Apply pagination
                var totalCount = bookings.Count;
                var pagedBookings = bookings
                    .Skip(parameters.Skip)
                    .Take(parameters.Take)
                    .ToList();

                var bookingDtos = _mapper.Map<List<BookingDto>>(pagedBookings);
                var result = PagedResult<BookingDto>.Create(bookingDtos, totalCount, parameters.PageNumber, parameters.PageSize);

                // Cache for 5 minutes
                await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(5));

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving bookings for user {UserId}", userId);
                throw;
            }
        }

        public async Task<PagedResult<BookingDto>> GetUserBookingsAsync(int userId, BookingStatus? status, int page, int pageSize)
        {
            try
            {
                var bookings = await _unitOfWork.Bookings.GetUserBookingsAsync(userId);
                var bookingDtos = _mapper.Map<List<BookingDto>>(bookings);

                // Filter by status if provided
                if (status.HasValue)
                {
                    bookingDtos = bookingDtos.Where(b => b.Status == status.Value).ToList();
                }

                var totalCount = bookingDtos.Count;
                var pagedBookings = bookingDtos
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PagedResult<BookingDto>.Create(pagedBookings, totalCount, page, pageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving bookings for user {UserId}", userId);
                throw;
            }
        }

        public async Task<BookingDto> CreateBookingAsync(CreateBookingRequest createBookingRequest, int userId)
        {
            try
            {
                // Validate trip availability
                var trip = await _unitOfWork.Trips.GetByIdAsync(createBookingRequest.TripId);
                if (trip == null || !trip.IsActive)
                {
                    throw new InvalidOperationException("Trip not found or not available");
                }

                if (!trip.IsAvailableForBooking(createBookingRequest.TravelDate, createBookingRequest.TotalPeople))
                {
                    throw new InvalidOperationException("Trip is not available for the selected date and number of people");
                }

                // Calculate pricing
                var pricePerPerson = trip.EffectivePrice;
                var totalAmount = pricePerPerson * createBookingRequest.TotalPeople;
                var discountAmount = 0m; // Apply any discounts here
                var finalAmount = totalAmount - discountAmount;

                // Create booking
                var booking = new Booking
                {
                    UserId = userId,
                    TripId = createBookingRequest.TripId,
                    TravelDate = createBookingRequest.TravelDate,
                    NumberOfAdults = createBookingRequest.NumberOfAdults,
                    NumberOfChildren = createBookingRequest.NumberOfChildren,
                    NumberOfInfants = createBookingRequest.NumberOfInfants,
                    PricePerPerson = pricePerPerson,
                    TotalAmount = totalAmount,
                    DiscountAmount = discountAmount,
                    FinalAmount = finalAmount,
                    SpecialRequests = createBookingRequest.SpecialRequests,
                    EmergencyContactName = createBookingRequest.EmergencyContactName,
                    EmergencyContactPhone = createBookingRequest.EmergencyContactPhone,
                    Status = BookingStatus.Pending,
                    PaymentStatus = TravelTourism.Core.Enums.PaymentStatus.Pending
                };

                // Generate unique booking number
                await _unitOfWork.Bookings.AddAsync(booking);
                await _unitOfWork.SaveChangesAsync();
                
                booking.BookingNumber = booking.GenerateBookingNumber();
                _unitOfWork.Bookings.Update(booking);
                await _unitOfWork.SaveChangesAsync();

                var bookingDto = _mapper.Map<BookingDto>(booking);
                
                // Clear user booking caches
                await ClearUserBookingCaches(userId);

                _logger.LogInformation("Booking created successfully with number {BookingNumber}", booking.BookingNumber);
                return bookingDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating booking for user {UserId}", userId);
                throw;
            }
        }

        public async Task<BookingDto> UpdateBookingAsync(int id, UpdateBookingRequest updateBookingRequest)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(id);
                if (booking == null)
                {
                    throw new InvalidOperationException("Booking not found");
                }

                // Update booking properties
                booking.TravelDate = updateBookingRequest.TravelDate;
                booking.NumberOfAdults = updateBookingRequest.NumberOfAdults;
                booking.NumberOfChildren = updateBookingRequest.NumberOfChildren;
                booking.NumberOfInfants = updateBookingRequest.NumberOfInfants;
                booking.SpecialRequests = updateBookingRequest.SpecialRequests;
                booking.EmergencyContactName = updateBookingRequest.EmergencyContactName;
                booking.EmergencyContactPhone = updateBookingRequest.EmergencyContactPhone;
                booking.UpdatedAt = DateTime.UtcNow;

                // Recalculate pricing if trip changed
                if (booking.TripId != updateBookingRequest.TripId)
                {
                    var trip = await _unitOfWork.Trips.GetByIdAsync(updateBookingRequest.TripId);
                    if (trip == null || !trip.IsActive)
                    {
                        throw new InvalidOperationException("New trip not found or not available");
                    }

                    booking.TripId = updateBookingRequest.TripId;
                    booking.PricePerPerson = trip.EffectivePrice;
                    booking.TotalAmount = trip.EffectivePrice * updateBookingRequest.TotalPeople;
                    booking.FinalAmount = booking.TotalAmount - booking.DiscountAmount;
                }
                else
                {
                    // Recalculate with same trip
                    var trip = await _unitOfWork.Trips.GetByIdAsync(booking.TripId);
                    booking.TotalAmount = trip.EffectivePrice * updateBookingRequest.TotalPeople;
                    booking.FinalAmount = booking.TotalAmount - booking.DiscountAmount;
                }

                _unitOfWork.Bookings.Update(booking);
                await _unitOfWork.SaveChangesAsync();

                var bookingDto = _mapper.Map<BookingDto>(booking);
                
                // Clear caches
                await ClearUserBookingCaches(booking.UserId);

                _logger.LogInformation("Booking updated successfully with ID {BookingId}", id);
                return bookingDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating booking with ID {BookingId}", id);
                throw;
            }
        }

        public async Task<bool> CancelBookingAsync(int id, string reason)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(id);
                if (booking == null)
                {
                    return false;
                }

                booking.Status = BookingStatus.Cancelled;
                booking.CancellationReason = reason;
                booking.CancelledAt = DateTime.UtcNow;
                booking.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.Bookings.Update(booking);
                await _unitOfWork.SaveChangesAsync();
                
                // Clear caches
                await ClearUserBookingCaches(booking.UserId);

                _logger.LogInformation("Booking cancelled successfully with ID {BookingId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling booking with ID {BookingId}", id);
                throw;
            }
        }

        public async Task<bool> ConfirmBookingAsync(int id)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(id);
                if (booking == null)
                {
                    return false;
                }

                booking.Status = BookingStatus.Confirmed;
                booking.ConfirmedAt = DateTime.UtcNow;
                booking.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.Bookings.Update(booking);
                await _unitOfWork.SaveChangesAsync();
                
                // Clear caches
                await ClearUserBookingCaches(booking.UserId);

                _logger.LogInformation("Booking confirmed successfully with ID {BookingId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming booking with ID {BookingId}", id);
                throw;
            }
        }

        public async Task<bool> ProcessPaymentAsync(int bookingId, string paymentMethod, string transactionId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(bookingId);
                if (booking == null)
                {
                    return false;
                }

                // Process payment through payment service
                var paymentRequest = new TravelTourism.Core.Interfaces.Services.PaymentRequest
                {
                    BookingNumber = booking.BookingNumber,
                    PaymentMethodId = paymentMethod,
                    Amount = booking.FinalAmount,
                    UserId = booking.UserId,
                    TripId = booking.TripId
                };
                var paymentResult = await _paymentService.ProcessPaymentAsync(paymentRequest);

                if (paymentResult.Success)
                {
                    booking.PaymentStatus = TravelTourism.Core.Enums.PaymentStatus.Paid;
                    booking.PaymentMethod = paymentMethod;
                    booking.TransactionId = paymentResult.TransactionId;
                    booking.PaidAt = DateTime.UtcNow;
                    booking.UpdatedAt = DateTime.UtcNow;

                    _unitOfWork.Bookings.Update(booking);
                    await _unitOfWork.SaveChangesAsync();
                    
                    // Clear caches
                    await ClearUserBookingCaches(booking.UserId);

                    _logger.LogInformation("Payment processed successfully for booking {BookingId}", bookingId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment for booking {BookingId}", bookingId);
                throw;
            }
        }

        public async Task<List<BookingDto>> GetBookingsByStatusAsync(BookingStatus status)
        {
            try
            {
                var cacheKey = $"bookings:status:{status}";
                var cachedBookings = await _cacheService.GetAsync<List<BookingDto>>(cacheKey);
                
                if (cachedBookings != null)
                {
                    return cachedBookings;
                }

                var bookings = await _unitOfWork.Bookings.GetAllAsync();
                bookings = bookings.Where(b => b.Status == status).ToList();

                var bookingDtos = _mapper.Map<List<BookingDto>>(bookings);
                
                // Cache for 5 minutes
                await _cacheService.SetAsync(cacheKey, bookingDtos, TimeSpan.FromMinutes(5));

                return bookingDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving bookings by status {Status}", status);
                throw;
            }
        }

        public async Task<BookingDto> GetBookingWithDetailsAsync(int id)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetBookingWithDetailsAsync(id);
                if (booking == null)
                {
                    throw new InvalidOperationException("Booking not found");
                }

                return _mapper.Map<BookingDto>(booking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving booking with details for ID {BookingId}", id);
                throw;
            }
        }

        private async Task ClearUserBookingCaches(int userId)
        {
            var cacheKeys = new[]
            {
                $"user_bookings:{userId}:*",
                "bookings:*"
            };

            foreach (var key in cacheKeys)
            {
                await _cacheService.RemoveAsync(key);
            }
        }

        // Additional methods needed by controllers
        public async Task<BookingDto?> GetBookingByIdAsync(int id, int userId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(id);
                if (booking == null || booking.UserId != userId)
                {
                    return null;
                }

                return _mapper.Map<BookingDto>(booking);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving booking with ID {BookingId} for user {UserId}", id, userId);
                throw;
            }
        }

        public async Task<bool> CancelBookingAsync(int id, int userId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(id);
                if (booking == null || booking.UserId != userId)
                {
                    return false;
                }

                if (booking.Status != BookingStatus.Pending && booking.Status != BookingStatus.Confirmed)
                {
                    return false;
                }

                booking.Status = BookingStatus.Cancelled;
                booking.CancelledAt = DateTime.UtcNow;
                booking.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.Bookings.Update(booking);
                await _unitOfWork.SaveChangesAsync();

                // Clear caches
                await ClearUserBookingCaches(userId);

                // Send cancellation email
                await _emailService.SendEmailAsync(booking.User.Email, "Booking Cancelled", $"Your booking {booking.BookingNumber} has been cancelled.");

                _logger.LogInformation("Booking {BookingId} cancelled by user {UserId}", id, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling booking {BookingId} for user {UserId}", id, userId);
                throw;
            }
        }

        public async Task<bool> ProcessPaymentAsync(int bookingId, string paymentMethod, string transactionId, int userId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(bookingId);
                if (booking == null || booking.UserId != userId)
                {
                    return false;
                }

                // Process payment using the payment service
                var paymentRequest = new TravelTourism.Core.Interfaces.Services.PaymentRequest
                {
                    BookingNumber = booking.BookingNumber,
                    PaymentMethodId = paymentMethod,
                    Amount = booking.FinalAmount,
                    Currency = "USD",
                    UserId = userId,
                    TripId = booking.TripId
                };

                var paymentResult = await _paymentService.ProcessPaymentAsync(paymentRequest);
                
                if (paymentResult.Success)
                {
                    booking.PaymentStatus = TravelTourism.Core.Enums.PaymentStatus.Paid;
                    booking.UpdatedAt = DateTime.UtcNow;
                    
                    _unitOfWork.Bookings.Update(booking);
                    await _unitOfWork.SaveChangesAsync();

                    // Clear caches
                    await ClearUserBookingCaches(userId);

                    // Send confirmation email
                    await _emailService.SendEmailAsync(booking.User.Email, "Payment Confirmed", $"Payment confirmed for booking {booking.BookingNumber}. Transaction ID: {paymentResult.TransactionId}");

                    _logger.LogInformation("Payment processed successfully for booking {BookingId}", bookingId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment for booking {BookingId}", bookingId);
                throw;
            }
        }

        public async Task<string> GetPaymentStatusAsync(int bookingId, int userId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(bookingId);
                if (booking == null || booking.UserId != userId)
                {
                    return "NotFound";
                }

                return booking.PaymentStatus.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment status for booking {BookingId}", bookingId);
                throw;
            }
        }

        public async Task<bool> ConfirmPaymentAsync(int bookingId, string transactionId, int userId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(bookingId);
                if (booking == null || booking.UserId != userId)
                {
                    return false;
                }

                // Confirm payment using the payment service
                var paymentResult = await _paymentService.ConfirmPaymentAsync(transactionId);
                
                if (paymentResult.Success)
                {
                    booking.PaymentStatus = TravelTourism.Core.Enums.PaymentStatus.Paid;
                    booking.Status = BookingStatus.Confirmed;
                    booking.UpdatedAt = DateTime.UtcNow;
                    
                    _unitOfWork.Bookings.Update(booking);
                    await _unitOfWork.SaveChangesAsync();

                    // Clear caches
                    await ClearUserBookingCaches(userId);

                    // Send confirmation email
                    await _emailService.SendBookingConfirmationAsync(booking.User.Email, booking.BookingNumber, "Trip");

                    _logger.LogInformation("Payment confirmed for booking {BookingId}", bookingId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming payment for booking {BookingId}", bookingId);
                throw;
            }
        }

        public async Task<bool> UpdateBookingAsync(int id, UpdateBookingRequest updateBookingRequest, int userId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(id);
                if (booking == null || booking.UserId != userId)
                {
                    return false;
                }

                // Only allow updates for pending bookings
                if (booking.Status != BookingStatus.Pending)
                {
                    return false;
                }

                // Update booking properties
                booking.TravelDate = updateBookingRequest.TravelDate;
                booking.NumberOfAdults = updateBookingRequest.NumberOfAdults;
                booking.NumberOfChildren = updateBookingRequest.NumberOfChildren;
                booking.NumberOfInfants = updateBookingRequest.NumberOfInfants;
                booking.SpecialRequests = updateBookingRequest.SpecialRequests;
                booking.EmergencyContactName = updateBookingRequest.EmergencyContactName;
                booking.EmergencyContactPhone = updateBookingRequest.EmergencyContactPhone;
                booking.UpdatedAt = DateTime.UtcNow;

                // Recalculate pricing if trip changed
                if (booking.TripId != updateBookingRequest.TripId)
                {
                    var newTrip = await _unitOfWork.Trips.GetByIdAsync(updateBookingRequest.TripId);
                    if (newTrip != null && newTrip.IsActive)
                    {
                        booking.TripId = updateBookingRequest.TripId;
                        booking.PricePerPerson = newTrip.EffectivePrice;
                        booking.TotalAmount = newTrip.EffectivePrice * updateBookingRequest.TotalPeople;
                        booking.FinalAmount = booking.TotalAmount - booking.DiscountAmount;
                    }
                }

                _unitOfWork.Bookings.Update(booking);
                await _unitOfWork.SaveChangesAsync();

                // Clear caches
                await ClearUserBookingCaches(userId);

                _logger.LogInformation("Booking {BookingId} updated by user {UserId}", id, userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating booking {BookingId} for user {UserId}", id, userId);
                throw;
            }
        }

        public async Task<string> GenerateInvoiceAsync(int bookingId, int userId)
        {
            try
            {
                var booking = await _unitOfWork.Bookings.GetByIdAsync(bookingId);
                if (booking == null || booking.UserId != userId)
                {
                    throw new InvalidOperationException("Booking not found or access denied");
                }

                // This is a placeholder implementation
                // In a real application, you would:
                // 1. Generate a proper PDF invoice
                // 2. Include company details, booking details, pricing breakdown
                // 3. Save the invoice to storage
                // 4. Return the invoice URL or file path

                var invoiceNumber = $"INV-{booking.BookingNumber}-{DateTime.UtcNow:yyyyMMdd}";
                var invoiceUrl = $"/invoices/{invoiceNumber}.pdf";

                _logger.LogInformation("Invoice generated for booking {BookingId}: {InvoiceNumber}", bookingId, invoiceNumber);
                return invoiceUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating invoice for booking {BookingId}", bookingId);
                throw;
            }
        }
    }
} 