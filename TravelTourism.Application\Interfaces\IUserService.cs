using System.Threading.Tasks;
using TravelTourism.Application.DTOs.User;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.Booking;

namespace TravelTourism.Application.Interfaces
{
    public interface IUserService
    {
        Task<UserProfileDto> GetProfileAsync(string userId);
        Task UpdateProfileAsync(string userId, UpdateUserProfileDto userProfileDto);
        Task DeleteAccountAsync(string userId);
        
        // Additional methods needed by controllers
        Task<UserDto> GetUserByIdAsync(int userId);
        Task<bool> UpdateUserAsync(int userId, UpdateUserDto updateUserDto);
        Task<bool> DeleteUserAsync(int userId);
        Task<PagedResult<BookingDto>> GetUserBookingsAsync(int userId, PaginationParameters parameters, DateTime? startDate = null, DateTime? endDate = null);
    }
}
