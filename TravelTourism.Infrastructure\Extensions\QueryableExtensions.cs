using Microsoft.EntityFrameworkCore;
using TravelTourism.Core.Entities.Base;
using TravelTourism.Core.Interfaces;
using TravelTourism.Infrastructure.Specifications;

namespace TravelTourism.Infrastructure.Extensions;

public static class QueryableExtensions
{
    public static IQueryable<T> ApplySpecification<T>(this IQueryable<T> query, ISpecification<T> spec) where T : BaseEntity
    {
        return SpecificationEvaluator<T>.GetQuery(query, spec);
    }
} 