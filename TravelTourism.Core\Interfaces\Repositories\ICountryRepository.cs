using System.Collections.Generic;
using System.Threading.Tasks;
using TravelTourism.Core.Entities.Common;

namespace TravelTourism.Core.Interfaces.Repositories
{
    public interface ICountryRepository : IGenericRepository<Country>
    {
        Task<IReadOnlyList<Country>> GetActiveCountriesAsync();
        Task<Country> GetByCodeAsync(string code);
        Task<Country> GetCountryWithCitiesAsync(int id);
    }
}
