using System.ComponentModel.DataAnnotations;

namespace TravelTourism.Application.DTOs.Common;

public class CurrencyDto
{
    public int Id { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(10)]
    public string Code { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(5)]
    public string Symbol { get; set; } = string.Empty;
    
    public decimal ExchangeRate { get; set; } = 1.0m;
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
} 