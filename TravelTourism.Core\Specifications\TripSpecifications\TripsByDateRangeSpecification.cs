using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.TripSpecifications;

public class TripsByDateRangeSpecification : BaseSpecification<Trip>
{
    public TripsByDateRangeSpecification(DateTime fromDate, DateTime toDate)
        : base(t => t.AvailableFrom >= fromDate && t.AvailableTo <= toDate && t.IsActive && !t.IsDeleted)
    {
        AddInclude(t => t.Category);
        AddInclude(t => t.DestinationCity);
        AddInclude(t => t.DepartureCity);
        AddInclude(t => t.Images);
        AddOrderBy(t => t.AvailableFrom);
    }
} 