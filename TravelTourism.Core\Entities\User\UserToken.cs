using System;
using TravelTourism.Core.Entities.Base;

namespace TravelTourism.Core.Entities.User
{
    public class UserToken : BaseEntity
    {
        public int UserId { get; set; }
        public string RefreshToken { get; set; }
        public DateTime ExpiryDate { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual User User { get; set; }

        public bool IsExpired => DateTime.UtcNow >= ExpiryDate;

        public void Revoke()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
