using Microsoft.EntityFrameworkCore;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Data.Seeds;

public class DefaultDataSeeder
{
    private readonly ApplicationDbContext _context;

    public DefaultDataSeeder(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task SeedAsync()
    {
        // Seed default data here
        await _context.SaveChangesAsync();
    }
} 