using TravelTourism.Core.Constants;

namespace TravelTourism.Application.DTOs.Common
{
    public class PaginationParameters
    {
        private int _pageSize = DomainConstants.Pagination.DefaultPageSize;
        private int _pageNumber = 1;

        public int PageNumber
        {
            get => _pageNumber;
            set => _pageNumber = value < 1 ? 1 : value;
        }

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = value > DomainConstants.Pagination.MaxPageSize 
                ? DomainConstants.Pagination.MaxPageSize 
                : value < 1 ? DomainConstants.Pagination.DefaultPageSize : value;
        }

        public int Skip => (PageNumber - 1) * PageSize;
        public int Take => PageSize;
    }
}
