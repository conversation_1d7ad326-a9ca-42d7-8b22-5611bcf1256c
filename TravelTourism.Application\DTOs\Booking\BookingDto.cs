using TravelTourism.Core.Enums;
using TravelTourism.Application.DTOs.Trip;
using TravelTourism.Application.DTOs.Auth;

namespace TravelTourism.Application.DTOs.Booking
{
    public class BookingDto
    {
        public int Id { get; set; }
        public string BookingNumber { get; set; } = string.Empty;
        public int UserId { get; set; }
        public int TripId { get; set; }
        public DateTime BookingDate { get; set; }
        public DateTime TravelDate { get; set; }
        public int NumberOfAdults { get; set; }
        public int NumberOfChildren { get; set; }
        public int NumberOfInfants { get; set; }
        public decimal PricePerPerson { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal FinalAmount { get; set; }
        public BookingStatus Status { get; set; }
        public PaymentStatus PaymentStatus { get; set; }
        public string SpecialRequests { get; set; } = string.Empty;
        public string EmergencyContactName { get; set; } = string.Empty;
        public string EmergencyContactPhone { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }

        // Navigation properties
        public UserDto? User { get; set; }
        public TripDto? Trip { get; set; }
        public List<BookingPaymentDto> Payments { get; set; } = new List<BookingPaymentDto>();

        // Computed properties
        public int TotalPeople => NumberOfAdults + NumberOfChildren + NumberOfInfants;
        public bool CanBeCancelled => Status == BookingStatus.Pending || Status == BookingStatus.Confirmed;
    }

    public class BookingDetailDto : BookingDto
    {
        public string CancellationReason { get; set; } = string.Empty;
        public DateTime? CancelledAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateBookingDto
    {
        public int TripId { get; set; }
        public DateTime TravelDate { get; set; }
        public int NumberOfAdults { get; set; } = 1;
        public int NumberOfChildren { get; set; } = 0;
        public int NumberOfInfants { get; set; } = 0;
        public decimal PricePerPerson { get; set; }
        public string SpecialRequests { get; set; } = string.Empty;
        public string EmergencyContactName { get; set; } = string.Empty;
        public string EmergencyContactPhone { get; set; } = string.Empty;
        
        // Computed property
        public int TotalPeople => NumberOfAdults + NumberOfChildren + NumberOfInfants;
    }

    public class UpdateBookingDto
    {
        public int TripId { get; set; }
        public DateTime TravelDate { get; set; }
        public int NumberOfAdults { get; set; }
        public int NumberOfChildren { get; set; }
        public int NumberOfInfants { get; set; }
        public decimal PricePerPerson { get; set; }
        public string SpecialRequests { get; set; } = string.Empty;
        public string EmergencyContactName { get; set; } = string.Empty;
        public string EmergencyContactPhone { get; set; } = string.Empty;
        
        // Computed property
        public int TotalPeople => NumberOfAdults + NumberOfChildren + NumberOfInfants;
    }

    public class BookingFilterDto
    {
        public int? UserId { get; set; }
        public int? TripId { get; set; }
        public BookingStatus? Status { get; set; }
        public PaymentStatus? PaymentStatus { get; set; }
        public DateTime? BookingDateFrom { get; set; }
        public DateTime? BookingDateTo { get; set; }
        public DateTime? TravelDateFrom { get; set; }
        public DateTime? TravelDateTo { get; set; }
        public string BookingNumber { get; set; } = string.Empty;
        public string SortBy { get; set; } = "BookingDate";
        public string SortDirection { get; set; } = "Desc";
    }

    public class BookingPaymentDto
    {
        public int Id { get; set; }
        public int BookingId { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public PaymentStatus Status { get; set; }
        public DateTime? PaymentDate { get; set; }
        public string FailureReason { get; set; } = string.Empty;
        public decimal? RefundAmount { get; set; }
        public DateTime? RefundDate { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreatePaymentDto
    {
        public int BookingId { get; set; }
        public string PaymentMethod { get; set; } = "Stripe";
        public string Currency { get; set; } = "USD";
    }

    public class PaymentResultDto
    {
        public bool Success { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string PaymentIntentId { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public PaymentStatus Status { get; set; }
    }

    public class CancelBookingDto
    {
        public string Reason { get; set; } = string.Empty;
    }
}
