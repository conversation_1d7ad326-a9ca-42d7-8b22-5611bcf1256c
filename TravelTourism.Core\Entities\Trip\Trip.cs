using System;
using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;
using TravelTourism.Core.Entities.Common;
using TravelTourism.Core.Enums;

namespace TravelTourism.Core.Entities.Trip
{
    public class Trip : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ShortDescription { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal? DiscountPrice { get; set; }
        public int Duration { get; set; } // in days
        public int MaxCapacity { get; set; }
        public int MaxGroupSize { get; set; }
        public int? MinAge { get; set; }
        public int? MaxAge { get; set; }
        public TripDifficulty Difficulty { get; set; }
        public int CategoryId { get; set; }
        public int DestinationCityId { get; set; }
        public int DepartureCityId { get; set; }
        public bool IncludesAccommodation { get; set; } = false;
        public bool IncludesTransport { get; set; } = false;
        public bool IncludesMeals { get; set; } = false;
        public bool IncludesGuide { get; set; } = false;
        public string MainImageUrl { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public bool IsFeatured { get; set; } = false;
        public DateTime AvailableFrom { get; set; }
        public DateTime AvailableTo { get; set; }

        // Navigation properties
        public virtual TripCategory Category { get; set; } = null!;
        public virtual City DestinationCity { get; set; } = null!;
        public virtual City DepartureCity { get; set; } = null!;
        public virtual ICollection<TripImage> Images { get; set; } = new List<TripImage>();
        public virtual ICollection<TripItinerary> Itineraries { get; set; } = new List<TripItinerary>();
        public virtual ICollection<Booking.Booking> Bookings { get; set; } = new List<Booking.Booking>();

        public decimal EffectivePrice => DiscountPrice ?? Price;
        public bool HasDiscount => DiscountPrice.HasValue && DiscountPrice.Value < Price;
        public decimal DiscountPercentage => HasDiscount ? ((Price - DiscountPrice.Value) / Price) * 100 : 0;

        public bool IsAvailable(DateTime date)
        {
            return IsActive && date >= AvailableFrom && date <= AvailableTo;
        }

        public bool IsAvailableForBooking(DateTime travelDate, int numberOfPeople)
        {
            if (!IsAvailable(travelDate))
                return false;

            // Check if there's capacity (this would need booking count logic)
            return numberOfPeople <= MaxCapacity;
        }
    }
}
