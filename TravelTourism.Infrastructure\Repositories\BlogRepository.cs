using Microsoft.EntityFrameworkCore;
using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Repositories
{
    public class BlogRepository : GenericRepository<Blog>, IBlogRepository
    {
        public BlogRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IReadOnlyList<Blog>> GetPublishedBlogsAsync()
        {
            return await _context.Blogs
                .Where(b => b.IsPublished && !b.IsDeleted)
                .Include(b => b.Author)
                .Include(b => b.Category)
                .Include(b => b.Tags)
                .OrderByDescending(b => b.PublishedAt)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Blog>> GetFeaturedBlogsAsync(int count = 10)
        {
            return await _context.Blogs
                .Where(b => b.IsFeatured && b.IsPublished && !b.IsDeleted)
                .Include(b => b.Author)
                .Include(b => b.Category)
                .Include(b => b.Tags)
                .OrderByDescending(b => b.PublishedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<Blog> GetBySlugAsync(string slug)
        {
            return await _context.Blogs
                .Where(b => b.Slug == slug && b.IsPublished && !b.IsDeleted)
                .Include(b => b.Author)
                .Include(b => b.Category)
                .Include(b => b.Tags)
                .Include(b => b.Images)
                .FirstOrDefaultAsync();
        }

        public async Task<IReadOnlyList<Blog>> GetBlogsByCategoryAsync(int categoryId)
        {
            return await _context.Blogs
                .Where(b => b.CategoryId == categoryId && b.IsPublished && !b.IsDeleted)
                .Include(b => b.Author)
                .Include(b => b.Category)
                .Include(b => b.Tags)
                .OrderByDescending(b => b.PublishedAt)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Blog>> GetBlogsByAuthorAsync(int authorId)
        {
            return await _context.Blogs
                .Where(b => b.AuthorId == authorId && b.IsPublished && !b.IsDeleted)
                .Include(b => b.Author)
                .Include(b => b.Category)
                .Include(b => b.Tags)
                .OrderByDescending(b => b.PublishedAt)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<BlogCategory>> GetCategoriesAsync()
        {
            return await _context.BlogCategories
                .Where(c => c.IsActive && !c.IsDeleted)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<BlogTag>> GetTagsAsync()
        {
            return await _context.BlogTags
                .Where(t => !t.IsDeleted)
                .OrderBy(t => t.Name)
                .ToListAsync();
        }

        public async Task<bool> IsSlugUniqueAsync(string slug, int? excludeBlogId = null)
        {
            var query = _context.Blogs.Where(b => b.Slug == slug && !b.IsDeleted);
            
            if (excludeBlogId.HasValue)
            {
                query = query.Where(b => b.Id != excludeBlogId.Value);
            }

            return !await query.AnyAsync();
        }
    }
}
