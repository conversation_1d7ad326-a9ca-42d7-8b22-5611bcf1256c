using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.API.Models.Requests;
using TravelTourism.Application.DTOs.Admin;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.User;
using TravelTourism.Application.Interfaces;
using TravelTourism.Core.Enums;

namespace TravelTourism.Controllers.Admin
{
    [ApiController]
    [Route("api/v1/admin/[controller]")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class AdminUsersController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IAdminService _adminService;

        public AdminUsersController(IUserService userService, IAdminService adminService)
        {
            _userService = userService;
            _adminService = adminService;
        }

        /// <summary>
        /// Get all users with pagination and filtering
        /// </summary>
        /// <param name="request">Users filter and pagination request</param>
        /// <returns>Paginated list of users</returns>
        [HttpGet]
        public async Task<IActionResult> GetUsers([FromQuery] GetUsersRequest request)
        {
            try
            {
                UserRole? role = null;
                if (!string.IsNullOrEmpty(request.Role) && Enum.TryParse<UserRole>(request.Role, true, out var parsedRole))
                {
                    role = parsedRole;
                }

                var filter = new UserFilterDto
                {
                    Role = role,
                    IsActive = request.IsActive,
                    SearchTerm = request.SearchTerm,
                    PageNumber = request.Page,
                    PageSize = request.PageSize
                };
                var users = await _adminService.GetUsersAsync(filter);
                return Ok(CreateSuccessResponse(users, "Users retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User details</returns>
        [HttpGet("{id:int}")]
        public async Task<IActionResult> GetUser(int id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);

                if (user == null)
                {
                    return NotFound(CreateErrorResponse("User not found", "USER_NOT_FOUND"));
                }

                return Ok(CreateSuccessResponse(user, "User retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create a new user
        /// </summary>
        /// <param name="request">User creation request</param>
        /// <returns>Created user details</returns>
        [HttpPost]
        public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var user = await _adminService.CreateUserAsync(request);

                return CreatedAtAction(
                    nameof(GetUser),
                    new { id = user.Id },
                    CreateSuccessResponse(user, "User created successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update user details
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="request">Update user request</param>
        /// <returns>Updated user details</returns>
        [HttpPut("{id:int}")]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] UpdateUserRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.UpdateUserAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "User updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete("{id:int}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            try
            {
                var result = await _adminService.DeleteUserAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "User deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Activate user account
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Activation result</returns>
        [HttpPost("{id:int}/activate")]
        public async Task<IActionResult> ActivateUser(int id)
        {
            try
            {
                var result = await _adminService.ActivateUserAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "User activated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Deactivate user account
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Deactivation result</returns>
        [HttpPost("{id:int}/deactivate")]
        public async Task<IActionResult> DeactivateUser(int id)
        {
            try
            {
                var result = await _adminService.DeactivateUserAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "User deactivated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Assign role to user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="request">Role assignment request</param>
        /// <returns>Role assignment result</returns>
        [HttpPost("{id:int}/roles")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> AssignRole(int id, [FromBody] AssignRoleRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.AssignRoleAsync(id, request.Role);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Role assigned successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Remove role from user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="roleName">Role name</param>
        /// <returns>Role removal result</returns>
        [HttpDelete("{id:int}/roles/{roleName}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> RemoveRole(int id, string roleName)
        {
            try
            {
                var result = await _adminService.RemoveRoleAsync(id, roleName);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Role removed successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get user's bookings
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="request">Pagination request</param>
        /// <returns>User's bookings</returns>
        [HttpGet("{id:int}/bookings")]
        public async Task<IActionResult> GetUserBookings(int id, [FromQuery] PaginationRequest request)
        {
            try
            {
                var pagination = new PaginationParameters
                {
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
                var bookings = await _adminService.GetUserBookingsAsync(id, pagination);
                return Ok(CreateSuccessResponse(bookings, "User bookings retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get user statistics
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User statistics</returns>
        [HttpGet("{id:int}/statistics")]
        public async Task<IActionResult> GetUserStatistics(int id)
        {
            try
            {
                var statistics = await _adminService.GetUserStatisticsAsync(id);
                return Ok(CreateSuccessResponse(statistics, "User statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Send notification to user
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="request">Notification request</param>
        /// <returns>Notification result</returns>
        [HttpPost("{id:int}/notifications")]
        public async Task<IActionResult> SendNotification(int id, [FromBody] SendNotificationRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.SendNotificationAsync(id, request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse<object>(null, "Notification sent successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Reset user password
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Password reset result</returns>
        [HttpPost("{id:int}/reset-password")]
        public async Task<IActionResult> ResetPassword(int id)
        {
            try
            {
                var result = await _adminService.ResetUserPasswordAsync(id);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Password reset successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}


