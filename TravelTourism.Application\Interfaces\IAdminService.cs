using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.User;
using TravelTourism.Application.DTOs.Admin;
using TravelTourism.Application.DTOs.Blog;
using TravelTourism.Application.DTOs.Booking;
using TravelTourism.Application.DTOs.Trip;
using Microsoft.AspNetCore.Http;

namespace TravelTourism.Application.Interfaces
{
    public interface IAdminService
    {
        // Dashboard and Statistics
        Task<DashboardStatsDto> GetDashboardStatsAsync();
        Task<object> GetDashboardOverviewAsync();
        Task<object> GetSystemStatisticsAsync();
        Task<object> GetRevenueAnalyticsAsync(RevenueAnalyticsRequest request);
        Task<object> GetUserAnalyticsAsync(UserAnalyticsRequest request);
        Task<object> GetBookingTrendsAsync(BookingTrendsRequest request);
        Task<object> GetPopularDestinationsAsync(PopularDestinationsRequest request);
        Task<object> GetRecentActivitiesAsync(RecentActivitiesRequest request);
        Task<object> GetPerformanceMetricsAsync(PerformanceMetricsRequest request);
        Task<object> GetSystemHealthStatusAsync();
        Task<object> GetAuditLogsAsync(AuditLogsRequest request);
        Task<object> GetSystemConfigurationsAsync();
        Task<ApiResponse> UpdateSystemConfigurationAsync(UpdateConfigurationRequest request);
        Task<object> GetDatabaseStatisticsAsync();
        Task<object> GetServerMetricsAsync();
        Task<object> GetApplicationLogsAsync(ApplicationLogsRequest request);
        Task<object> GetErrorLogsAsync(ErrorLogsRequest request);
        Task<ApiResponse> ClearApplicationCacheAsync();
        Task<ApiResponse> SendSystemNotificationAsync(SendSystemNotificationRequest request);
        Task<object> ExportSystemDataAsync(ExportSystemDataRequest request);
        Task<object> GetBackupStatusAsync();
        Task<ApiResponse> CreateSystemBackupAsync(CreateBackupRequest request);
        Task<object> GetMaintenanceModeStatusAsync();
        Task<ApiResponse> ToggleMaintenanceModeAsync(ToggleMaintenanceModeRequest request);

        // User Management
        Task<PagedResult<AdminUserDto>> GetUsersAsync(UserFilterDto filter);
        Task<AdminUserDto> CreateUserAsync(CreateUserRequest request);
        Task<ApiResponse> UpdateUserAsync(int id, UpdateUserRequest request);
        Task<ApiResponse> DeleteUserAsync(int id);
        Task<ApiResponse> DeactivateUserAsync(int userId);
        Task<ApiResponse> ActivateUserAsync(int userId);
        Task<ApiResponse> PromoteToAdminAsync(int userId);
        Task<ApiResponse> DemoteFromAdminAsync(int userId);
        Task<ApiResponse> AssignRoleAsync(int userId, string roleName);
        Task<ApiResponse> RemoveRoleAsync(int userId, string roleName);
        Task<object> GetUserBookingsAsync(int userId, PaginationParameters request);
        Task<object> GetUserStatisticsAsync(int userId);
        Task<ApiResponse> SendNotificationAsync(int userId, SendNotificationRequest request);
        Task<ApiResponse> ResetUserPasswordAsync(int userId);
        Task<List<MonthlyStatsDto>> GetMonthlyStatsAsync(int year);

        // Trip Management
        Task<PagedResult<TripDto>> GetTripsAsync(AdminTripFilterRequest request);
        Task<ApiResponse> PublishTripAsync(int id);
        Task<ApiResponse> UnpublishTripAsync(int id);
        Task<ApiResponse> FeatureTripAsync(int id);
        Task<ApiResponse> UnfeatureTripAsync(int id);
        Task<object> GetTripBookingsAsync(int id, PaginationParameters request);
        Task<object> GetTripStatisticsAsync(int id);
        Task<ApiResponse> UploadTripImagesAsync(int id, List<IFormFile> files);
        Task<ApiResponse> DeleteTripImageAsync(int id, int imageId);
        Task<ApiResponse> DuplicateTripAsync(int id);
        Task<PagedResult<object>> GetTripReviewsAsync(int tripId, PaginationParameters request);
        Task<ApiResponse> BulkUpdateTripsAsync(BulkUpdateTripsRequest request);
        Task<ApiResponse<byte[]>> ExportTripsAsync(ExportTripsRequest request);

        // Blog Management
        Task<PagedResult<BlogDto>> GetBlogsAsync(AdminBlogFilterRequest request);
        Task<ApiResponse> PublishBlogAsync(int id);
        Task<ApiResponse> UnpublishBlogAsync(int id);
        Task<ApiResponse> FeatureBlogAsync(int id);
        Task<ApiResponse> UnfeatureBlogAsync(int id);
        Task<object> GetBlogStatisticsAsync(int id);
        Task<ApiResponse> UploadBlogCoverImageAsync(int id, IFormFile file);
        Task<ApiResponse> DeleteBlogCoverImageAsync(int id);
        Task<object> GetBlogCommentsAsync(int id, PaginationParameters request);
        Task<ApiResponse> ApproveBlogCommentAsync(int id, int commentId);
        Task<ApiResponse> RejectBlogCommentAsync(int id, int commentId);
        Task<ApiResponse> DeleteBlogCommentAsync(int id, int commentId);
        Task<List<BlogCategoryDto>> GetBlogCategoriesAsync();
        Task<BlogCategoryDto> CreateBlogCategoryAsync(CreateBlogCategoryRequest request);
        Task<ApiResponse> UpdateBlogCategoryAsync(int id, UpdateBlogCategoryRequest request);
        Task<ApiResponse> DeleteBlogCategoryAsync(int id);
        Task<List<BlogTagDto>> GetBlogTagsAsync();
        Task<BlogTagDto> CreateBlogTagAsync(CreateBlogTagRequest request);
        Task<ApiResponse> UpdateBlogTagAsync(int id, UpdateBlogTagRequest request);
        Task<ApiResponse> DeleteBlogTagAsync(int id);
        Task<ApiResponse> BulkUpdateBlogsAsync(BulkUpdateBlogsRequest request);
        Task<ApiResponse<byte[]>> ExportBlogsAsync(ExportBlogsRequest request);

        // Booking Management
        Task<PagedResult<BookingDto>> GetBookingsAsync(AdminBookingFilterRequest request);
        Task<ApiResponse> ConfirmBookingAsync(int id);
        Task<ApiResponse> ProcessRefundAsync(int id, ProcessRefundRequest request);
        Task<ApiResponse> UpdateBookingStatusAsync(int id, UpdateBookingStatusRequest request);
        Task<object> GetBookingStatisticsAsync();
        Task<object> GetRevenueStatisticsAsync(RevenueStatisticsRequest request);
        Task<object> GetBookingAnalyticsAsync(BookingAnalyticsRequest request);
        Task<object> GetBookingReportsAsync(BookingReportRequest request);
        Task<object> ExportBookingsAsync(ExportBookingsRequest request);
        Task<object> ExportBookingInvoicesAsync(ExportInvoicesRequest request);
        Task<ApiResponse> SendBookingReminderAsync(int id, SendReminderRequest request);
        Task<object> GetBookingPaymentAsync(int id);
        Task<object> GetBookingCustomerAsync(int id);
        Task<object> GetBookingTripAsync(int id);
        Task<ApiResponse> AddBookingNoteAsync(int id, AddBookingNoteRequest request);
        Task<object> GetBookingNotesAsync(int id);
        Task<ApiResponse> UpdateBookingNoteAsync(int id, int noteId, UpdateBookingNoteRequest request);
        Task<ApiResponse> DeleteBookingNoteAsync(int id, int noteId);
        Task<ApiResponse> BulkUpdateBookingsAsync(BulkUpdateBookingsRequest request);
        Task<object> GetBookingActivityLogAsync(int id);

        // System
        Task<ApiResponse> ClearSystemCacheAsync();
    }
} 