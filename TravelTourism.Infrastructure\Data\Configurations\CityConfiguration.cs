using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TravelTourism.Core.Entities.Common;

namespace TravelTourism.Infrastructure.Data.Configurations;

public class CityConfiguration : IEntityTypeConfiguration<City>
{
    public void Configure(EntityTypeBuilder<City> builder)
    {
        builder.ToTable("Cities");

        builder.HasKey(c => c.Id);

        builder.Property(c => c.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.StateProvince)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(c => c.TimeZone)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(c => c.Latitude)
            .HasColumnType("decimal(10,8)");

        builder.Property(c => c.Longitude)
            .HasColumnType("decimal(11,8)");

        builder.Property(c => c.IsPopular)
            .HasDefaultValue(false);

        builder.Property(c => c.IsActive)
            .HasDefaultValue(true);

        // Relationships
        builder.HasOne(c => c.Country)
            .WithMany(co => co.Cities)
            .HasForeignKey(c => c.CountryId)
            .OnDelete(DeleteBehavior.Restrict);

        // The Trip relationships are configured in TripConfiguration
        // to avoid circular dependencies
    }
}