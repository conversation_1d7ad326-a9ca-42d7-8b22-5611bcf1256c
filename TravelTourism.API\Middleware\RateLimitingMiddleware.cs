using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace TravelTourism.API.Middleware;

public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;

    public RateLimitingMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Add rate limiting logic here
        await _next(context);
    }
} 