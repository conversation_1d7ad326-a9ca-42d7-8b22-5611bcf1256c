using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.TripSpecifications;

public class TripsByPriceRangeSpecification : BaseSpecification<Trip>
{
    public TripsByPriceRangeSpecification(decimal minPrice, decimal maxPrice)
        : base(t => t.Price >= minPrice && t.Price <= maxPrice && t.IsActive && !t.IsDeleted)
    {
        AddInclude(t => t.Category);
        AddInclude(t => t.DestinationCity);
        AddInclude(t => t.DepartureCity);
        AddInclude(t => t.Images);
        AddOrderBy(t => t.Price);
    }
} 