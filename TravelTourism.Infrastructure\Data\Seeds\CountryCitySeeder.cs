using Microsoft.EntityFrameworkCore;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Data.Seeds;

public class CountryCitySeeder
{
    private readonly ApplicationDbContext _context;

    public CountryCitySeeder(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task SeedAsync()
    {
        // Seed countries and cities here
        await _context.SaveChangesAsync();
    }
} 