namespace TravelTourism.Application.DTOs.Common
{
    public class FileUploadDto
    {
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
        public byte[] Content { get; set; } = Array.Empty<byte>();
    }

    public class FileUploadResultDto
    {
        public string Url { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string PublicId { get; set; } = string.Empty;
        public long Size { get; set; }
    }
}
