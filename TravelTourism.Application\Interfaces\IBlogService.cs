using TravelTourism.Application.DTOs.Blog;
using TravelTourism.Application.DTOs.Common;

namespace TravelTourism.Application.Interfaces;

public interface IBlogService
{
    Task<PagedResult<BlogDto>> GetBlogsAsync(PaginationParameters parameters);
    Task<BlogDto?> GetBlogByIdAsync(int id);
    Task<BlogDto?> GetBlogBySlugAsync(string slug);
    Task<PagedResult<BlogDto>> GetBlogsByCategoryAsync(int categoryId, PaginationParameters parameters);
    Task<List<BlogDto>> GetFeaturedBlogsAsync();
    Task<List<BlogCategoryDto>> GetBlogCategoriesAsync();
    Task<BlogDto> CreateBlogAsync(CreateBlogRequest createBlogRequest, int authorId);
    Task<BlogDto> UpdateBlogAsync(int id, UpdateBlogRequest updateBlogRequest);
    Task<ApiResponse> DeleteBlogAsync(int id);
    Task<ApiResponse> PublishBlogAsync(int id);
    Task<ApiResponse> UnpublishBlogAsync(int id);
    Task<ApiResponse> ToggleFeaturedAsync(int id);
    Task IncrementViewCountAsync(int id);
    
    // Additional methods needed by controllers
    Task<PagedResult<BlogDto>> GetBlogsAsync(PaginationParameters parameters, string? searchTerm, int? categoryId, string? author, string? status, string? sortBy);
    Task<List<BlogDto>> GetFeaturedBlogsAsync(PaginationParameters parameters);
    Task<PagedResult<BlogDto>> GetBlogsByCategoryAsync(int categoryId, PaginationParameters parameters, string? searchTerm);
    Task<List<BlogDto>> GetRelatedBlogsAsync(int blogId, int count = 5);
} 