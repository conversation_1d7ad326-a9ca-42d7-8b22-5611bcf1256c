using TravelTourism.Core.Enums;
using TravelTourism.Application.DTOs.Common;

namespace TravelTourism.Application.DTOs.Trip;

public class TripFilterDto : PaginationParameters
{
    public string? SearchTerm { get; set; }
    public int? CategoryId { get; set; }
    public int? DestinationId { get; set; }
    public int? DestinationCityId { get; set; }
    public int? DepartureCityId { get; set; }
    public TripDifficulty? Difficulty { get; set; }
    public TripDifficulty? DifficultyLevel { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public int? MinDuration { get; set; }
    public int? MaxDuration { get; set; }
    public DateTime? AvailableFrom { get; set; }
    public DateTime? AvailableTo { get; set; }
    public bool? IncludesAccommodation { get; set; }
    public bool? IncludesTransport { get; set; }
    public bool? IncludesMeals { get; set; }
    public bool? IncludesGuide { get; set; }
    public bool? IsFeatured { get; set; }
    public string? SortBy { get; set; } = "Name";
    public string? SortOrder { get; set; } = "Asc";
    public string? SortDirection { get; set; } = "Asc";
} 