using TravelTourism.Core.Entities.Booking;
using TravelTourism.Core.Enums;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BookingSpecifications;

public class BookingsByStatusSpecification : BaseSpecification<Booking>
{
    public BookingsByStatusSpecification(BookingStatus status)
        : base(b => b.Status == status && !b.<PERSON>)
    {
        AddInclude(b => b.Trip);
        AddInclude(b => b.User);
        AddInclude(b => b.BookingPayments);
        AddOrderByDescending(b => b.CreatedAt);
    }
} 