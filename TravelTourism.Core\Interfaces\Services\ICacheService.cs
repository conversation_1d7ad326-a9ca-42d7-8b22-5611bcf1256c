using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TravelTourism.Core.Interfaces.Services
{
    public interface ICacheService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;
        Task<bool> RemoveAsync(string key);
        Task<bool> ExistsAsync(string key);
        Task SetHashAsync<T>(string key, string field, T value) where T : class;
        Task<T?> GetHashAsync<T>(string key, string field) where T : class;
        Task<bool> RemoveHashAsync(string key, string field);
        Task SetListAsync<T>(string key, IEnumerable<T> values, TimeSpan? expiry = null) where T : class;
        Task<List<T>> GetListAsync<T>(string key, long start = 0, long stop = -1) where T : class;
        Task<bool> SetExpiryAsync(string key, TimeSpan expiry);
        Task<TimeSpan?> GetExpiryAsync(string key);
        Task<bool> ClearAllAsync();
        Task<long> GetDatabaseSizeAsync();
    }
}
