using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Models.Requests;

namespace TravelTourism.Application.Interfaces
{
    public interface IFileService
    {
        Task<ApiResponse<string>> UploadImageAsync(FileUploadRequest request);
        Task<ApiResponse<string>> UploadDocumentAsync(FileUploadRequest request);
        Task<ApiResponse> DeleteFileAsync(string fileUrl);
        Task<ApiResponse<string>> GetFileUrlAsync(string fileId);
        Task<ApiResponse<List<string>>> GetFilesInFolderAsync(string folder);
        Task<ApiResponse<string>> OptimizeImageAsync(string imageUrl, int? width = null, int? height = null, string format = "auto");
    }
} 