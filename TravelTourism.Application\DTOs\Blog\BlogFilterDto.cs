using TravelTourism.Application.DTOs.Common;

namespace TravelTourism.Application.DTOs.Blog
{
    public class BlogFilterDto : PaginationParameters
    {
        public int? CategoryId { get; set; }
        public bool? IsPublished { get; set; } = true;
        public string? SearchTerm { get; set; }
        public bool? IsFeatured { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? AuthorId { get; set; }
    }
} 