using TravelTourism.Core.Entities.Base;

namespace TravelTourism.Core.Entities.Trip
{
    public class TripImage : BaseEntity
    {
        public int TripId { get; set; }
        public string ImageUrl { get; set; }
        public string Caption { get; set; }
        public bool IsMain { get; set; } = false;
        public int DisplayOrder { get; set; } = 0;

        // Navigation properties
        public virtual Trip Trip { get; set; }
    }
}
