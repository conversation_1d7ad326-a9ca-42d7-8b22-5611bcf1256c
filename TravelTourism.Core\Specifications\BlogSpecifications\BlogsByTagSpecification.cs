using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BlogSpecifications;

public class BlogsByTagSpecification : BaseSpecification<Blog>
{
    public BlogsByTagSpecification(string tagName)
        : base(b => b.BlogTags.Any(bt => bt.Name == tagName) && b.IsPublished && !b.<PERSON>)
    {
        AddInclude(b => b.Category);
        AddInclude(b => b.Author);
        AddInclude(b => b.BlogImages);
        AddInclude(b => b.BlogTags);
        AddOrderByDescending(b => b.PublishedAt);
    }
} 