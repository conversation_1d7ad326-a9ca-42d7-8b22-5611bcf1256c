using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BlogSpecifications;

public class BlogsByTagSpecification : BaseSpecification<Blog>
{
    public BlogsByTagSpecification(string tagName)
        : base(b => b.Tags.Any(bt => bt.Name == tagName) && b.IsPublished && !b.Is<PERSON>eleted)
    {
        AddInclude(b => b.Category);
        AddInclude(b => b.Author);
        AddInclude(b => b.Images);
        AddInclude(b => b.Tags);
        AddOrderByDescending(b => b.PublishedAt);
    }
} 