using Microsoft.AspNetCore.Mvc;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.API.Controllers.Base;

namespace TravelTourism.API.Controllers.V1;

[ApiController]
[Route("api/v1/[controller]")]
public class CitiesController : BaseController
{
    private readonly ICityRepository _cityRepository;

    public CitiesController(ICityRepository cityRepository)
    {
        _cityRepository = cityRepository;
    }

    [HttpGet]
    public async Task<IActionResult> GetCities([FromQuery] int? countryId)
    {
        var cities = countryId.HasValue 
            ? await _cityRepository.GetCitiesByCountryAsync(countryId.Value)
            : await _cityRepository.GetAllAsync();
        
        return Ok(cities);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetCity(int id)
    {
        var city = await _cityRepository.GetByIdAsync(id);
        if (city == null)
            return NotFound();
        
        return Ok(city);
    }
} 