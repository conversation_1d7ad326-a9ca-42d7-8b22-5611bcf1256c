using TravelTourism.Core.Enums;
using TravelTourism.Application.DTOs.Common;

namespace TravelTourism.Application.DTOs.Trip
{
    public class TripDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string ShortDescription { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal? DiscountPrice { get; set; }
        public int Duration { get; set; }
        public int MaxCapacity { get; set; }
        public int? MinAge { get; set; }
        public int? MaxAge { get; set; }
        public TripDifficulty Difficulty { get; set; }
        public string MainImageUrl { get; set; } = string.Empty;
        public bool IsFeatured { get; set; }
        public DateTime AvailableFrom { get; set; }
        public DateTime AvailableTo { get; set; }
        public TripCategoryDto Category { get; set; } = new TripCategoryDto();
        public CityDto DestinationCity { get; set; } = new CityDto();
        public CityDto DepartureCity { get; set; } = new CityDto();
        public List<TripImageDto> Images { get; set; } = new List<TripImageDto>();

        // Computed properties
        public decimal EffectivePrice => DiscountPrice ?? Price;
        public bool HasDiscount => DiscountPrice.HasValue && DiscountPrice.Value < Price;
        public decimal DiscountPercentage => HasDiscount ? ((Price - DiscountPrice.Value) / Price) * 100 : 0;
    }

    public class TripDetailDto : TripDto
    {
        public string Description { get; set; } = string.Empty;
        public bool IncludesAccommodation { get; set; }
        public bool IncludesTransport { get; set; }
        public bool IncludesMeals { get; set; }
        public bool IncludesGuide { get; set; }
        public List<TripItineraryDto> Itineraries { get; set; } = new List<TripItineraryDto>();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class TripImageDto
    {
        public int Id { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string Caption { get; set; } = string.Empty;
        public bool IsMain { get; set; }
        public int DisplayOrder { get; set; }
    }

    public class TripItineraryDto
    {
        public int Id { get; set; }
        public int Day { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Activities { get; set; } = string.Empty;
        public string Meals { get; set; } = string.Empty;
        public string Accommodation { get; set; } = string.Empty;
    }

    public class CreateTripItineraryDto
    {
        public int Day { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Activities { get; set; } = string.Empty;
        public string Meals { get; set; } = string.Empty;
        public string Accommodation { get; set; } = string.Empty;
    }

    public class TripCategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string IconUrl { get; set; } = string.Empty;
    }
}
