using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BlogSpecifications;

public class BlogsByAuthorSpecification : BaseSpecification<Blog>
{
    public BlogsByAuthorSpecification(int authorId)
        : base(b => b.AuthorId == authorId && !b.Is<PERSON>eleted)
    {
        AddInclude(b => b.Category);
        AddInclude(b => b.Author);
        AddInclude(b => b.Images);
        AddOrderByDescending(b => b.CreatedAt);
    }
} 