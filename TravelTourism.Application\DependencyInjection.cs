using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using TravelTourism.Application.Interfaces;
using TravelTourism.Application.Services;

namespace TravelTourism.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            // AutoMapper
            services.AddAutoMapper(Assembly.GetExecutingAssembly());

            // FluentValidation
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

            // Application Services
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ITripService, TripService>();
            services.AddScoped<IBlogService, BlogService>();
            services.AddScoped<IBookingService, BookingService>();
            services.AddScoped<IFileService, FileService>();
            services.AddScoped<IAdminService, AdminService>();

            return services;
        }
    }
}
