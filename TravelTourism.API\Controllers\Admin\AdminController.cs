using Microsoft.AspNetCore.Mvc;
using TravelTourism.Application.Interfaces;
using TravelTourism.API.Controllers.Base;

namespace TravelTourism.API.Controllers.Admin;

[ApiController]
[Route("api/v1/admin")]
public class AdminController : BaseController
{
    private readonly IAdminService _adminService;

    public AdminController(IAdminService adminService)
    {
        _adminService = adminService;
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow });
    }
} 