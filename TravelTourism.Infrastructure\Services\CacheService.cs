using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Infrastructure.Services
{
    public class CacheService : ICacheService
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly ILogger<CacheService> _logger;
        private readonly IDatabase _database;
        private readonly JsonSerializerOptions _jsonOptions;

        public CacheService(IConfiguration configuration, ILogger<CacheService> logger)
        {
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            try
            {
                var connectionString = configuration["Redis:ConnectionString"] ?? "localhost:6379";
                _redis = ConnectionMultiplexer.Connect(connectionString);
                _database = _redis.GetDatabase();
                
                _logger.LogInformation("Redis cache service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Redis cache service");
                throw;
            }
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                var value = await _database.StringGetAsync(key);
                if (value.HasValue)
                {
                    var result = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                    _logger.LogDebug("Cache hit for key: {Key}", key);
                    return result;
                }

                _logger.LogDebug("Cache miss for key: {Key}", key);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cache for key: {Key}", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
        {
            try
            {
                var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
                await _database.StringSetAsync(key, serializedValue, expiry);
                _logger.LogDebug("Cache set for key: {Key} with expiry: {Expiry}", key, expiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache for key: {Key}", key);
            }
        }

        public async Task<bool> RemoveAsync(string key)
        {
            try
            {
                var result = await _database.KeyDeleteAsync(key);
                _logger.LogDebug("Cache removed for key: {Key}, Result: {Result}", key, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache for key: {Key}", key);
                return false;
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                var result = await _database.KeyExistsAsync(key);
                _logger.LogDebug("Cache exists check for key: {Key}, Result: {Result}", key, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
                return false;
            }
        }

        public async Task SetHashAsync<T>(string key, string field, T value) where T : class
        {
            try
            {
                var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
                await _database.HashSetAsync(key, field, serializedValue);
                _logger.LogDebug("Cache hash set for key: {Key}, field: {Field}", key, field);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache hash for key: {Key}, field: {Field}", key, field);
            }
        }

        public async Task<T?> GetHashAsync<T>(string key, string field) where T : class
        {
            try
            {
                var value = await _database.HashGetAsync(key, field);
                if (value.HasValue)
                {
                    var result = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                    _logger.LogDebug("Cache hash hit for key: {Key}, field: {Field}", key, field);
                    return result;
                }

                _logger.LogDebug("Cache hash miss for key: {Key}, field: {Field}", key, field);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cache hash for key: {Key}, field: {Field}", key, field);
                return null;
            }
        }

        public async Task<bool> RemoveHashAsync(string key, string field)
        {
            try
            {
                var result = await _database.HashDeleteAsync(key, field);
                _logger.LogDebug("Cache hash removed for key: {Key}, field: {Field}, Result: {Result}", key, field, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache hash for key: {Key}, field: {Field}", key, field);
                return false;
            }
        }

        public async Task SetListAsync<T>(string key, IEnumerable<T> values, TimeSpan? expiry = null) where T : class
        {
            try
            {
                var serializedValues = values.Select(v => JsonSerializer.Serialize(v, _jsonOptions)).ToArray();
                await _database.ListRightPushAsync(key, serializedValues.Select(v => (RedisValue)v).ToArray());
                
                if (expiry.HasValue)
                {
                    await _database.KeyExpireAsync(key, expiry.Value);
                }

                _logger.LogDebug("Cache list set for key: {Key} with {Count} items", key, values.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache list for key: {Key}", key);
            }
        }

        public async Task<List<T>> GetListAsync<T>(string key, long start = 0, long stop = -1) where T : class
        {
            try
            {
                var values = await _database.ListRangeAsync(key, start, stop);
                var result = new List<T>();

                foreach (var value in values)
                {
                    if (value.HasValue)
                    {
                        var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                        if (item != null)
                        {
                            result.Add(item);
                        }
                    }
                }

                _logger.LogDebug("Cache list retrieved for key: {Key}, Count: {Count}", key, result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cache list for key: {Key}", key);
                return new List<T>();
            }
        }

        public async Task<bool> SetExpiryAsync(string key, TimeSpan expiry)
        {
            try
            {
                var result = await _database.KeyExpireAsync(key, expiry);
                _logger.LogDebug("Cache expiry set for key: {Key}, Expiry: {Expiry}, Result: {Result}", key, expiry, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache expiry for key: {Key}", key);
                return false;
            }
        }

        public async Task<TimeSpan?> GetExpiryAsync(string key)
        {
            try
            {
                var expiry = await _database.KeyTimeToLiveAsync(key);
                _logger.LogDebug("Cache expiry retrieved for key: {Key}, Expiry: {Expiry}", key, expiry);
                return expiry;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cache expiry for key: {Key}", key);
                return null;
            }
        }

        public async Task<bool> ClearAllAsync()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                await server.FlushDatabaseAsync();
                _logger.LogInformation("All cache cleared successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing all cache");
                return false;
            }
        }

        public async Task<long> GetDatabaseSizeAsync()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var size = await server.DatabaseSizeAsync();
                _logger.LogDebug("Cache database size: {Size}", size);
                return size;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache database size");
                return 0;
            }
        }

        public void Dispose()
        {
            _redis?.Close();
            _redis?.Dispose();
        }
    }
} 