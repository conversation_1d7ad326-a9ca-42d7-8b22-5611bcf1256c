using Microsoft.Extensions.DependencyInjection;
using TravelTourism.Core.Interfaces;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.Core.Interfaces.Services;
using TravelTourism.Infrastructure.Repositories;
using TravelTourism.Infrastructure.Services;

namespace TravelTourism.Infrastructure.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services)
    {
        // Register repositories
        services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<ITripRepository, TripRepository>();
        services.AddScoped<IBlogRepository, BlogRepository>();
        services.AddScoped<IBookingRepository, BookingRepository>();
        services.AddScoped<ICountryRepository, CountryRepository>();
        services.AddScoped<ICityRepository, CityRepository>();
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Register services
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<IFileStorageService, FileStorageService>();
        services.AddScoped<IPaymentService, PaymentService>();
        services.AddScoped<ICacheService, CacheService>();

        return services;
    }
} 