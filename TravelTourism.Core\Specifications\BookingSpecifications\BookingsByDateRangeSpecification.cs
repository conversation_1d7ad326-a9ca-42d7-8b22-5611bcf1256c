using TravelTourism.Core.Entities.Booking;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BookingSpecifications;

public class BookingsByDateRangeSpecification : BaseSpecification<Booking>
{
    public BookingsByDateRangeSpecification(DateTime fromDate, DateTime toDate)
        : base(b => b.TravelDate >= fromDate && b.TravelDate <= toDate && !b.IsDeleted)
    {
        AddInclude(b => b.Trip);
        AddInclude(b => b.User);
        AddInclude(b => b.BookingPayments);
        AddOrderByDescending(b => b.TravelDate);
    }
} 