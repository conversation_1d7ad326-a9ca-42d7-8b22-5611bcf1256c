using TravelTourism.Core.Entities.User;
using TravelTourism.Core.Enums;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.UserSpecifications;

public class UserByRoleSpecification : BaseSpecification<User>
{
    public UserByRoleSpecification(UserRole role)
        : base(u => u.Role == role && u.IsActive && !u.IsDeleted)
    {
        AddOrderBy(u => u.FirstName);
    }
} 