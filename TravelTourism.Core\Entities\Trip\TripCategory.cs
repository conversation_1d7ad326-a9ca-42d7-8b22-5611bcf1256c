using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;

namespace TravelTourism.Core.Entities.Trip
{
    public class TripCategory : BaseEntity
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string IconUrl { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Trip> Trips { get; set; } = new List<Trip>();
    }
}
