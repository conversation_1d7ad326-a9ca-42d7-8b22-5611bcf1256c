using TravelTourism.Core.Entities.Booking;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.BookingSpecifications;

public class BookingsByTripSpecification : BaseSpecification<Booking>
{
    public BookingsByTripSpecification(int tripId)
        : base(b => b.TripId == tripId && !b.<PERSON>)
    {
        AddInclude(b => b.Trip);
        AddInclude(b => b.User);
        AddInclude(b => b.BookingPayments);
        AddOrderByDescending(b => b.CreatedAt);
    }
} 