using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.TripSpecifications;

public class TripsByCategorySpecification : BaseSpecification<Trip>
{
    public TripsByCategorySpecification(int categoryId)
        : base(t => t.CategoryId == categoryId && t.IsActive && !t.IsDeleted)
    {
        AddInclude(t => t.Category);
        AddInclude(t => t.DestinationCity);
        AddInclude(t => t.DepartureCity);
        AddInclude(t => t.Images);
        AddOrderBy(t => t.Name);
    }
} 