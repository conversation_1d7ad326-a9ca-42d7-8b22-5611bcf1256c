using System;
using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;

namespace TravelTourism.Core.Entities.Blog
{
    public class Blog : BaseEntity
    {
        public string Title { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public int AuthorId { get; set; }
        public int CategoryId { get; set; }
        public string FeaturedImageUrl { get; set; } = string.Empty;
        public bool IsPublished { get; set; } = false;
        public bool IsFeatured { get; set; } = false;
        public bool IsActive { get; set; } = true;
        public DateTime? PublishedAt { get; set; }
        public int ViewCount { get; set; } = 0;
        public int ReadingTime { get; set; } // in minutes

        // Navigation properties
        public virtual User.User Author { get; set; } = null!;
        public virtual BlogCategory Category { get; set; } = null!;
        public virtual ICollection<BlogImage> Images { get; set; } = new List<BlogImage>();
        public virtual ICollection<BlogTag> Tags { get; set; } = new List<BlogTag>();
        
        // Additional navigation properties for specifications
        public virtual ICollection<BlogImage> BlogImages => Images;
        public virtual ICollection<BlogTag> BlogTags => Tags;

        public void Publish()
        {
            IsPublished = true;
            PublishedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Unpublish()
        {
            IsPublished = false;
            PublishedAt = null;
            UpdatedAt = DateTime.UtcNow;
        }

        public void IncrementViewCount()
        {
            ViewCount++;
            UpdatedAt = DateTime.UtcNow;
        }

        public string GenerateSlug()
        {
            if (string.IsNullOrWhiteSpace(Title))
                return string.Empty;

            return Title.ToLowerInvariant()
                .Replace(" ", "-")
                .Replace("&", "and")
                .Replace("?", "")
                .Replace("!", "")
                .Replace(",", "")
                .Replace(".", "")
                .Replace("'", "")
                .Replace("\"", "");
        }
    }
}
