namespace TravelTourism.Application.DTOs.Admin
{
    public class ExportTripsRequest
    {
        public string Format { get; set; } = "csv"; // "csv", "excel", "pdf"
        public string? SearchTerm { get; set; }
        public int? CategoryId { get; set; }
        public int? CityId { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsFeatured { get; set; }
        public DateTime? StartDateFrom { get; set; }
        public DateTime? StartDateTo { get; set; }
        public List<string> Fields { get; set; } = new();
    }
} 