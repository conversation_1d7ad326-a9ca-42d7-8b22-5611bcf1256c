using TravelTourism.Core.Entities.Trip;
using TravelTourism.Core.Specifications.Base;

namespace TravelTourism.Core.Specifications.TripSpecifications;

public class AvailableTripsSpecification : BaseSpecification<Trip>
{
    public AvailableTripsSpecification()
        : base(t => t.IsActive && !t.IsDeleted && t.AvailableFrom <= DateTime.UtcNow && t.AvailableTo >= DateTime.UtcNow)
    {
        AddInclude(t => t.Category);
        AddInclude(t => t.DestinationCity);
        AddInclude(t => t.DepartureCity);
        AddInclude(t => t.TripImages);
        AddOrderBy(t => t.Name);
    }
} 