using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.DTOs.Admin;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;

namespace TravelTourism.Controllers.Admin
{
    [ApiController]
    [Route("api/v1/admin/[controller]")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class AdminDashboardController : BaseController
    {
        private readonly IAdminService _adminService;

        public AdminDashboardController(IAdminService adminService)
        {
            _adminService = adminService;
        }

        /// <summary>
        /// Get dashboard overview statistics
        /// </summary>
        /// <returns>Dashboard overview data</returns>
        [HttpGet("overview")]
        public async Task<IActionResult> GetDashboardOverview()
        {
            try
            {
                var overview = await _adminService.GetDashboardOverviewAsync();
                return Ok(CreateSuccessResponse(overview, "Dashboard overview retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get system statistics
        /// </summary>
        /// <returns>System statistics</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetSystemStatistics()
        {
            try
            {
                var statistics = await _adminService.GetSystemStatisticsAsync();
                return Ok(CreateSuccessResponse(statistics, "System statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get revenue analytics
        /// </summary>
        /// <param name="request">Revenue analytics request</param>
        /// <returns>Revenue analytics data</returns>
        [HttpGet("revenue-analytics")]
        public async Task<IActionResult> GetRevenueAnalytics([FromQuery] RevenueAnalyticsRequest request)
        {
            try
            {
                var analytics = await _adminService.GetRevenueAnalyticsAsync(request);
                return Ok(CreateSuccessResponse(analytics, "Revenue analytics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get user analytics
        /// </summary>
        /// <param name="request">User analytics request</param>
        /// <returns>User analytics data</returns>
        [HttpGet("user-analytics")]
        public async Task<IActionResult> GetUserAnalytics([FromQuery] UserAnalyticsRequest request)
        {
            try
            {
                var analytics = await _adminService.GetUserAnalyticsAsync(request);
                return Ok(CreateSuccessResponse(analytics, "User analytics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get booking trends
        /// </summary>
        /// <param name="request">Booking trends request</param>
        /// <returns>Booking trends data</returns>
        [HttpGet("booking-trends")]
        public async Task<IActionResult> GetBookingTrends([FromQuery] BookingTrendsRequest request)
        {
            try
            {
                var trends = await _adminService.GetBookingTrendsAsync(request);
                return Ok(CreateSuccessResponse(trends, "Booking trends retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get popular destinations
        /// </summary>
        /// <param name="request">Popular destinations request</param>
        /// <returns>Popular destinations data</returns>
        [HttpGet("popular-destinations")]
        public async Task<IActionResult> GetPopularDestinations([FromQuery] PopularDestinationsRequest request)
        {
            try
            {
                var destinations = await _adminService.GetPopularDestinationsAsync(request);
                return Ok(CreateSuccessResponse(destinations, "Popular destinations retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get recent activities
        /// </summary>
        /// <param name="request">Recent activities request</param>
        /// <returns>Recent activities data</returns>
        [HttpGet("recent-activities")]
        public async Task<IActionResult> GetRecentActivities([FromQuery] RecentActivitiesRequest request)
        {
            try
            {
                var activities = await _adminService.GetRecentActivitiesAsync(request);
                return Ok(CreateSuccessResponse(activities, "Recent activities retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get performance metrics
        /// </summary>
        /// <param name="request">Performance metrics request</param>
        /// <returns>Performance metrics data</returns>
        [HttpGet("performance-metrics")]
        public async Task<IActionResult> GetPerformanceMetrics([FromQuery] PerformanceMetricsRequest request)
        {
            try
            {
                var metrics = await _adminService.GetPerformanceMetricsAsync(request);
                return Ok(CreateSuccessResponse(metrics, "Performance metrics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get system health status
        /// </summary>
        /// <returns>System health status</returns>
        [HttpGet("health-status")]
        public async Task<IActionResult> GetSystemHealthStatus()
        {
            try
            {
                var healthStatus = await _adminService.GetSystemHealthStatusAsync();
                return Ok(CreateSuccessResponse(healthStatus, "System health status retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get audit logs
        /// </summary>
        /// <param name="request">Audit logs request</param>
        /// <returns>Audit logs data</returns>
        [HttpGet("audit-logs")]
        public async Task<IActionResult> GetAuditLogs([FromQuery] AuditLogsRequest request)
        {
            try
            {
                var logs = await _adminService.GetAuditLogsAsync(request);
                return Ok(CreateSuccessResponse(logs, "Audit logs retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get system configurations
        /// </summary>
        /// <returns>System configurations</returns>
        [HttpGet("configurations")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetSystemConfigurations()
        {
            try
            {
                var configurations = await _adminService.GetSystemConfigurationsAsync();
                return Ok(CreateSuccessResponse(configurations, "System configurations retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update system configuration
        /// </summary>
        /// <param name="request">Update configuration request</param>
        /// <returns>Update result</returns>
        [HttpPut("configurations")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> UpdateSystemConfiguration([FromBody] UpdateConfigurationRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.UpdateSystemConfigurationAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "System configuration updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get database statistics
        /// </summary>
        /// <returns>Database statistics</returns>
        [HttpGet("database-statistics")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetDatabaseStatistics()
        {
            try
            {
                var statistics = await _adminService.GetDatabaseStatisticsAsync();
                return Ok(CreateSuccessResponse(statistics, "Database statistics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get server metrics
        /// </summary>
        /// <returns>Server metrics</returns>
        [HttpGet("server-metrics")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetServerMetrics()
        {
            try
            {
                var metrics = await _adminService.GetServerMetricsAsync();
                return Ok(CreateSuccessResponse(metrics, "Server metrics retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get application logs
        /// </summary>
        /// <param name="request">Application logs request</param>
        /// <returns>Application logs</returns>
        [HttpGet("application-logs")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetApplicationLogs([FromQuery] ApplicationLogsRequest request)
        {
            try
            {
                var logs = await _adminService.GetApplicationLogsAsync(request);
                return Ok(CreateSuccessResponse(logs, "Application logs retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get error logs
        /// </summary>
        /// <param name="request">Error logs request</param>
        /// <returns>Error logs</returns>
        [HttpGet("error-logs")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetErrorLogs([FromQuery] ErrorLogsRequest request)
        {
            try
            {
                var logs = await _adminService.GetErrorLogsAsync(request);
                return Ok(CreateSuccessResponse(logs, "Error logs retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Clear application cache
        /// </summary>
        /// <returns>Clear cache result</returns>
        [HttpPost("clear-cache")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> ClearApplicationCache()
        {
            try
            {
                var result = await _adminService.ClearApplicationCacheAsync();

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Application cache cleared successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Send system notification
        /// </summary>
        /// <param name="request">System notification request</param>
        /// <returns>Notification result</returns>
        [HttpPost("send-notification")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> SendSystemNotification([FromBody] SendSystemNotificationRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.SendSystemNotificationAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "System notification sent successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Export system data
        /// </summary>
        /// <param name="request">Export data request</param>
        /// <returns>Export result</returns>
        [HttpPost("export-data")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> ExportSystemData([FromBody] ExportSystemDataRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.ExportSystemDataAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return File(result.Data, "application/zip", "system-data.zip");
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get backup status
        /// </summary>
        /// <returns>Backup status</returns>
        [HttpGet("backup-status")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetBackupStatus()
        {
            try
            {
                var status = await _adminService.GetBackupStatusAsync();
                return Ok(CreateSuccessResponse(status, "Backup status retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Create system backup
        /// </summary>
        /// <param name="request">Backup request</param>
        /// <returns>Backup result</returns>
        [HttpPost("create-backup")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> CreateSystemBackup([FromBody] CreateBackupRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.CreateSystemBackupAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "System backup created successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get maintenance mode status
        /// </summary>
        /// <returns>Maintenance mode status</returns>
        [HttpGet("maintenance-mode")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> GetMaintenanceModeStatus()
        {
            try
            {
                var status = await _adminService.GetMaintenanceModeStatusAsync();
                return Ok(CreateSuccessResponse(status, "Maintenance mode status retrieved successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Toggle maintenance mode
        /// </summary>
        /// <param name="request">Toggle maintenance mode request</param>
        /// <returns>Toggle result</returns>
        [HttpPost("toggle-maintenance-mode")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> ToggleMaintenanceMode([FromBody] ToggleMaintenanceModeRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(CreateValidationErrorResponse(ModelState));
                }

                var result = await _adminService.ToggleMaintenanceModeAsync(request);

                if (!result.Success)
                {
                    return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
                }

                return Ok(CreateSuccessResponse(result.Data, "Maintenance mode toggled successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}


