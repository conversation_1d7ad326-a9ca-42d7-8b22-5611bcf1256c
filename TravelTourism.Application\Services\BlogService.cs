using AutoMapper;
using Microsoft.Extensions.Logging;
using TravelTourism.Application.DTOs.Blog;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;
using TravelTourism.Core.Entities.Blog;
using TravelTourism.Core.Interfaces;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Application.Services
{
    public class BlogService : IBlogService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<BlogService> _logger;
        private readonly IFileStorageService _fileStorageService;
        private readonly ICacheService _cacheService;

        public BlogService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<BlogService> logger,
            IFileStorageService fileStorageService,
            ICacheService cacheService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _fileStorageService = fileStorageService;
            _cacheService = cacheService;
        }

        public async Task<PagedResult<BlogDto>> GetBlogsAsync(PaginationParameters parameters)
        {
            try
            {
                var cacheKey = $"blogs:{parameters.PageNumber}:{parameters.PageSize}";
                var cachedResult = await _cacheService.GetAsync<PagedResult<BlogDto>>(cacheKey);
                
                if (cachedResult != null)
                {
                    return cachedResult;
                }

                var blogs = await _unitOfWork.Blogs.GetPublishedBlogsAsync();

                // Apply pagination
                var totalCount = blogs.Count;
                var pagedBlogs = blogs
                    .Skip(parameters.Skip)
                    .Take(parameters.Take)
                    .ToList();

                var blogDtos = _mapper.Map<List<BlogDto>>(pagedBlogs);
                var result = PagedResult<BlogDto>.Create(blogDtos, totalCount, parameters.PageNumber, parameters.PageSize);

                // Cache the result for 5 minutes
                await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(5));

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blogs");
                throw;
            }
        }

        public async Task<BlogDto?> GetBlogByIdAsync(int id)
        {
            try
            {
                var cacheKey = $"blog:{id}";
                var cachedBlog = await _cacheService.GetAsync<BlogDto>(cacheKey);
                
                if (cachedBlog != null)
                {
                    return cachedBlog;
                }

                var blog = await _unitOfWork.Blogs.GetByIdAsync(id);
                if (blog == null || (!blog.IsPublished && !blog.IsActive))
                {
                    return null;
                }

                var blogDto = _mapper.Map<BlogDto>(blog);
                
                // Cache for 10 minutes
                await _cacheService.SetAsync(cacheKey, blogDto, TimeSpan.FromMinutes(10));

                return blogDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blog with ID {BlogId}", id);
                throw;
            }
        }

        public async Task<BlogDto?> GetBlogBySlugAsync(string slug)
        {
            try
            {
                var cacheKey = $"blog:slug:{slug}";
                var cachedBlog = await _cacheService.GetAsync<BlogDto>(cacheKey);
                
                if (cachedBlog != null)
                {
                    return cachedBlog;
                }

                var blog = await _unitOfWork.Blogs.GetBySlugAsync(slug);
                if (blog == null)
                {
                    return null;
                }

                var blogDto = _mapper.Map<BlogDto>(blog);
                
                // Cache for 10 minutes
                await _cacheService.SetAsync(cacheKey, blogDto, TimeSpan.FromMinutes(10));

                return blogDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blog with slug {Slug}", slug);
                throw;
            }
        }

        public async Task<PagedResult<BlogDto>> GetBlogsByCategoryAsync(int categoryId, PaginationParameters parameters)
        {
            try
            {
                var cacheKey = $"blogs:category:{categoryId}:{parameters.PageNumber}:{parameters.PageSize}";
                var cachedResult = await _cacheService.GetAsync<PagedResult<BlogDto>>(cacheKey);
                
                if (cachedResult != null)
                {
                    return cachedResult;
                }

                var blogs = await _unitOfWork.Blogs.GetPublishedBlogsAsync();
                blogs = blogs.Where(b => b.CategoryId == categoryId).ToList();

                // Apply pagination
                var totalCount = blogs.Count;
                var pagedBlogs = blogs
                    .Skip(parameters.Skip)
                    .Take(parameters.Take)
                    .ToList();

                var blogDtos = _mapper.Map<List<BlogDto>>(pagedBlogs);
                var result = PagedResult<BlogDto>.Create(blogDtos, totalCount, parameters.PageNumber, parameters.PageSize);

                // Cache for 5 minutes
                await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(5));

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blogs by category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<List<BlogDto>> GetFeaturedBlogsAsync()
        {
            try
            {
                var cacheKey = "featured_blogs";
                var cachedBlogs = await _cacheService.GetAsync<List<BlogDto>>(cacheKey);
                
                if (cachedBlogs != null)
                {
                    return cachedBlogs;
                }

                var blogs = await _unitOfWork.Blogs.GetFeaturedBlogsAsync(10);
                var blogDtos = _mapper.Map<List<BlogDto>>(blogs);
                
                // Cache for 15 minutes
                await _cacheService.SetAsync(cacheKey, blogDtos, TimeSpan.FromMinutes(15));

                return blogDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured blogs");
                throw;
            }
        }

        public async Task<List<BlogCategoryDto>> GetBlogCategoriesAsync()
        {
            try
            {
                var cacheKey = "blog_categories";
                var cachedCategories = await _cacheService.GetAsync<List<BlogCategoryDto>>(cacheKey);
                
                if (cachedCategories != null)
                {
                    return cachedCategories;
                }

                var categories = await _unitOfWork.Blogs.GetCategoriesAsync();
                var categoryDtos = _mapper.Map<List<BlogCategoryDto>>(categories);
                
                // Cache for 30 minutes
                await _cacheService.SetAsync(cacheKey, categoryDtos, TimeSpan.FromMinutes(30));

                return categoryDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blog categories");
                throw;
            }
        }

        public async Task<BlogDto> CreateBlogAsync(CreateBlogRequest createBlogRequest, int authorId)
        {
            try
            {
                var blog = _mapper.Map<Blog>(createBlogRequest);
                blog.AuthorId = authorId;
                blog.Slug = blog.GenerateSlug();

                // Handle image upload if provided
                if (!string.IsNullOrEmpty(createBlogRequest.FeaturedImageUrl))
                {
                    blog.FeaturedImageUrl = createBlogRequest.FeaturedImageUrl;
                }

                await _unitOfWork.Blogs.AddAsync(blog);
                await _unitOfWork.SaveChangesAsync();

                var blogDto = _mapper.Map<BlogDto>(blog);
                
                // Clear caches
                await ClearBlogCaches();

                _logger.LogInformation("Blog created successfully with ID {BlogId}", blog.Id);
                return blogDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating blog for author {AuthorId}", authorId);
                throw;
            }
        }

        public async Task<BlogDto> UpdateBlogAsync(int id, UpdateBlogRequest updateBlogRequest)
        {
            try
            {
                var blog = await _unitOfWork.Blogs.GetByIdAsync(id);
                if (blog == null)
                {
                    throw new InvalidOperationException("Blog not found");
                }

                // Update properties
                blog.Title = updateBlogRequest.Title;
                blog.Content = updateBlogRequest.Content;
                blog.CategoryId = updateBlogRequest.CategoryId;
                blog.Slug = blog.GenerateSlug();
                blog.UpdatedAt = DateTime.UtcNow;

                // Handle image update if provided
                if (!string.IsNullOrEmpty(updateBlogRequest.FeaturedImageUrl))
                {
                    blog.FeaturedImageUrl = updateBlogRequest.FeaturedImageUrl;
                }

                _unitOfWork.Blogs.Update(blog);
                await _unitOfWork.SaveChangesAsync();

                var blogDto = _mapper.Map<BlogDto>(blog);
                
                // Clear caches
                await ClearBlogCaches();

                _logger.LogInformation("Blog updated successfully with ID {BlogId}", id);
                return blogDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating blog with ID {BlogId}", id);
                throw;
            }
        }

        public async Task<ApiResponse> DeleteBlogAsync(int id)
        {
            try
            {
                var blog = await _unitOfWork.Blogs.GetByIdAsync(id);
                if (blog == null)
                {
                    return ApiResponse.ErrorResponse("Blog not found", new List<string> { "BLOG_NOT_FOUND" });
                }

                _unitOfWork.Blogs.Delete(blog);
                await _unitOfWork.SaveChangesAsync();

                // Clear caches
                await ClearBlogCaches();

                _logger.LogInformation("Blog deleted successfully with ID {BlogId}", id);
                return ApiResponse.SuccessResponse("Blog deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting blog with ID {BlogId}", id);
                return ApiResponse.ErrorResponse("Error deleting blog", new List<string> { "INTERNAL_SERVER_ERROR" });
            }
        }

        public async Task<ApiResponse> PublishBlogAsync(int id)
        {
            try
            {
                var blog = await _unitOfWork.Blogs.GetByIdAsync(id);
                if (blog == null)
                {
                    return ApiResponse.ErrorResponse("Blog not found", new List<string> { "BLOG_NOT_FOUND" });
                }

                blog.IsPublished = true;
                blog.PublishedAt = DateTime.UtcNow;
                blog.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.Blogs.Update(blog);
                await _unitOfWork.SaveChangesAsync();

                // Clear caches
                await ClearBlogCaches();

                _logger.LogInformation("Blog published successfully with ID {BlogId}", id);
                return ApiResponse.SuccessResponse("Blog published successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing blog with ID {BlogId}", id);
                return ApiResponse.ErrorResponse("Error publishing blog", new List<string> { "INTERNAL_SERVER_ERROR" });
            }
        }

        public async Task<ApiResponse> UnpublishBlogAsync(int id)
        {
            try
            {
                var blog = await _unitOfWork.Blogs.GetByIdAsync(id);
                if (blog == null)
                {
                    return ApiResponse.ErrorResponse("Blog not found", new List<string> { "BLOG_NOT_FOUND" });
                }

                blog.IsPublished = false;
                blog.PublishedAt = null;
                blog.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.Blogs.Update(blog);
                await _unitOfWork.SaveChangesAsync();

                // Clear caches
                await ClearBlogCaches();

                _logger.LogInformation("Blog unpublished successfully with ID {BlogId}", id);
                return ApiResponse.SuccessResponse("Blog unpublished successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unpublishing blog with ID {BlogId}", id);
                return ApiResponse.ErrorResponse("Error unpublishing blog", new List<string> { "INTERNAL_SERVER_ERROR" });
            }
        }

        public async Task<ApiResponse> ToggleFeaturedAsync(int id)
        {
            try
            {
                var blog = await _unitOfWork.Blogs.GetByIdAsync(id);
                if (blog == null)
                {
                    return ApiResponse.ErrorResponse("Blog not found", new List<string> { "BLOG_NOT_FOUND" });
                }

                blog.IsFeatured = !blog.IsFeatured;
                blog.UpdatedAt = DateTime.UtcNow;

                _unitOfWork.Blogs.Update(blog);
                await _unitOfWork.SaveChangesAsync();

                // Clear caches
                await ClearBlogCaches();

                _logger.LogInformation("Blog featured status toggled successfully with ID {BlogId}", id);
                return ApiResponse.SuccessResponse("Blog featured status toggled successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling featured status for blog with ID {BlogId}", id);
                return ApiResponse.ErrorResponse("Error toggling featured status", new List<string> { "INTERNAL_SERVER_ERROR" });
            }
        }

        public async Task IncrementViewCountAsync(int id)
        {
            try
            {
                var blog = await _unitOfWork.Blogs.GetByIdAsync(id);
                if (blog != null)
                {
                    blog.ViewCount++;
                    blog.UpdatedAt = DateTime.UtcNow;
                    _unitOfWork.Blogs.Update(blog);
                    await _unitOfWork.SaveChangesAsync();

                    // Clear cache
                    await _cacheService.RemoveAsync($"blog:{id}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing view count for blog {BlogId}", id);
                throw;
            }
        }

        // Additional methods needed by controllers
        public async Task<PagedResult<BlogDto>> GetBlogsAsync(PaginationParameters parameters, string? searchTerm, int? categoryId, string? author, string? status, string? sortBy)
        {
            try
            {
                var blogs = await _unitOfWork.Blogs.GetAllAsync();

                // Apply filters
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    blogs = blogs.Where(b => 
                        b.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        b.Content.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                if (categoryId.HasValue)
                {
                    blogs = blogs.Where(b => b.CategoryId == categoryId.Value).ToList();
                }

                if (!string.IsNullOrEmpty(author))
                {
                    blogs = blogs.Where(b => b.Author.FirstName.Contains(author, StringComparison.OrdinalIgnoreCase) ||
                                           b.Author.LastName.Contains(author, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                if (!string.IsNullOrEmpty(status))
                {
                    switch (status.ToLower())
                    {
                        case "published":
                            blogs = blogs.Where(b => b.IsPublished).ToList();
                            break;
                        case "draft":
                            blogs = blogs.Where(b => !b.IsPublished).ToList();
                            break;
                        case "featured":
                            blogs = blogs.Where(b => b.IsFeatured).ToList();
                            break;
                    }
                }

                // Apply sorting
                if (!string.IsNullOrEmpty(sortBy))
                {
                    switch (sortBy.ToLower())
                    {
                        case "title":
                            blogs = blogs.OrderBy(b => b.Title).ToList();
                            break;
                        case "createdat":
                            blogs = blogs.OrderByDescending(b => b.CreatedAt).ToList();
                            break;
                        case "viewcount":
                            blogs = blogs.OrderByDescending(b => b.ViewCount).ToList();
                            break;
                        default:
                            blogs = blogs.OrderByDescending(b => b.CreatedAt).ToList();
                            break;
                    }
                }

                // Apply pagination
                var totalCount = blogs.Count;
                var pagedBlogs = blogs
                    .Skip(parameters.Skip)
                    .Take(parameters.Take)
                    .ToList();

                var blogDtos = _mapper.Map<List<BlogDto>>(pagedBlogs);
                return PagedResult<BlogDto>.Create(blogDtos, totalCount, parameters.PageNumber, parameters.PageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blogs with filters");
                throw;
            }
        }

        public async Task<List<BlogDto>> GetFeaturedBlogsAsync(PaginationParameters parameters)
        {
            try
            {
                var blogs = await _unitOfWork.Blogs.GetFeaturedBlogsAsync(parameters.Take);
                var blogDtos = _mapper.Map<List<BlogDto>>(blogs);
                return blogDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving featured blogs");
                throw;
            }
        }

        public async Task<PagedResult<BlogDto>> GetBlogsByCategoryAsync(int categoryId, PaginationParameters parameters, string? searchTerm)
        {
            try
            {
                var blogs = await _unitOfWork.Blogs.GetAllAsync();
                blogs = blogs.Where(b => b.CategoryId == categoryId).ToList();

                // Apply search filter if provided
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    blogs = blogs.Where(b => 
                        b.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        b.Content.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Apply pagination
                var totalCount = blogs.Count;
                var pagedBlogs = blogs
                    .Skip(parameters.Skip)
                    .Take(parameters.Take)
                    .ToList();

                var blogDtos = _mapper.Map<List<BlogDto>>(pagedBlogs);
                return PagedResult<BlogDto>.Create(blogDtos, totalCount, parameters.PageNumber, parameters.PageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving blogs by category {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<List<BlogDto>> GetRelatedBlogsAsync(int blogId, int count = 5)
        {
            try
            {
                var currentBlog = await _unitOfWork.Blogs.GetByIdAsync(blogId);
                if (currentBlog == null)
                {
                    return new List<BlogDto>();
                }

                var allBlogs = await _unitOfWork.Blogs.GetPublishedBlogsAsync();
                var relatedBlogs = allBlogs
                    .Where(b => b.Id != blogId && 
                               (b.CategoryId == currentBlog.CategoryId || 
                                b.Tags.Any(t => currentBlog.Tags.Any(ct => ct.Name == t.Name))))
                    .OrderByDescending(b => b.ViewCount)
                    .Take(count)
                    .ToList();

                var blogDtos = _mapper.Map<List<BlogDto>>(relatedBlogs);
                return blogDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related blogs for blog {BlogId}", blogId);
                throw;
            }
        }

        private async Task ClearBlogCaches()
        {
            var cacheKeys = new[]
            {
                "featured_blogs",
                "blog_categories"
            };

            foreach (var key in cacheKeys)
            {
                await _cacheService.RemoveAsync(key);
            }

            // Clear paginated blog caches
            for (int page = 1; page <= 10; page++)
            {
                for (int pageSize = 10; pageSize <= 50; pageSize += 10)
                {
                    await _cacheService.RemoveAsync($"blogs:{page}:{pageSize}");
                }
            }
        }
    }
} 