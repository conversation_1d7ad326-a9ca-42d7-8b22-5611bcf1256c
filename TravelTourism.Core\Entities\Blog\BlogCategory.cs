using System.Collections.Generic;
using TravelTourism.Core.Entities.Base;

namespace TravelTourism.Core.Entities.Blog
{
    public class BlogCategory : BaseEntity
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<Blog> Blogs { get; set; } = new List<Blog>();
    }
}
