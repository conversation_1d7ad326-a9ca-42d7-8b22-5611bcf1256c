using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Stripe;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Infrastructure.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<PaymentService> _logger;

        public PaymentService(IConfiguration configuration, ILogger<PaymentService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            // Configure Stripe
            var stripeSecretKey = _configuration["PaymentSettings:Stripe:SecretKey"];
            if (!string.IsNullOrEmpty(stripeSecretKey))
            {
                StripeConfiguration.ApiKey = stripeSecretKey;
            }
        }

        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger.LogInformation("Processing payment for booking {BookingNumber}", paymentRequest.BookingNumber);

                var paymentIntentOptions = new PaymentIntentCreateOptions
                {
                    Amount = (long)(paymentRequest.Amount * 100), // Convert to cents
                    Currency = paymentRequest.Currency?.ToLower() ?? "usd",
                    PaymentMethod = paymentRequest.PaymentMethodId,
                    ConfirmationMethod = "manual",
                    Confirm = true,
                    Description = $"Payment for booking {paymentRequest.BookingNumber}",
                    Metadata = new Dictionary<string, string>
                    {
                        ["booking_number"] = paymentRequest.BookingNumber,
                        ["user_id"] = paymentRequest.UserId.ToString(),
                        ["trip_id"] = paymentRequest.TripId.ToString()
                    }
                };

                var paymentIntentService = new PaymentIntentService();
                var paymentIntent = await paymentIntentService.CreateAsync(paymentIntentOptions);

                if (paymentIntent.Status == "succeeded")
                {
                    _logger.LogInformation("Payment successful for booking {BookingNumber}, Payment Intent: {PaymentIntentId}", 
                        paymentRequest.BookingNumber, paymentIntent.Id);

                    return new PaymentResult
                    {
                        Success = true,
                        TransactionId = paymentIntent.Id,
                        Amount = paymentRequest.Amount,
                        Currency = paymentRequest.Currency,
                        Status = PaymentStatus.Success,
                        Message = "Payment processed successfully"
                    };
                }
                else if (paymentIntent.Status == "requires_action")
                {
                    _logger.LogInformation("Payment requires additional action for booking {BookingNumber}", paymentRequest.BookingNumber);

                    return new PaymentResult
                    {
                        Success = false,
                        TransactionId = paymentIntent.Id,
                        Amount = paymentRequest.Amount,
                        Currency = paymentRequest.Currency,
                        Status = PaymentStatus.RequiresAction,
                        Message = "Payment requires additional authentication",
                        ClientSecret = paymentIntent.ClientSecret
                    };
                }
                else
                {
                    _logger.LogWarning("Payment failed for booking {BookingNumber}, Status: {Status}", 
                        paymentRequest.BookingNumber, paymentIntent.Status);

                    return new PaymentResult
                    {
                        Success = false,
                        TransactionId = paymentIntent.Id,
                        Amount = paymentRequest.Amount,
                        Currency = paymentRequest.Currency,
                        Status = PaymentStatus.Failed,
                        Message = $"Payment failed: {paymentIntent.Status}"
                    };
                }
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Stripe payment error for booking {BookingNumber}", paymentRequest.BookingNumber);

                return new PaymentResult
                {
                    Success = false,
                    Amount = paymentRequest.Amount,
                    Currency = paymentRequest.Currency,
                    Status = PaymentStatus.Failed,
                    Message = $"Payment error: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected payment error for booking {BookingNumber}", paymentRequest.BookingNumber);

                return new PaymentResult
                {
                    Success = false,
                    Amount = paymentRequest.Amount,
                    Currency = paymentRequest.Currency,
                    Status = PaymentStatus.Failed,
                    Message = "An unexpected error occurred during payment processing"
                };
            }
        }

        public async Task<PaymentResult> ConfirmPaymentAsync(string paymentIntentId)
        {
            try
            {
                _logger.LogInformation("Confirming payment intent {PaymentIntentId}", paymentIntentId);

                var paymentIntentService = new PaymentIntentService();
                var paymentIntent = await paymentIntentService.ConfirmAsync(paymentIntentId);

                if (paymentIntent.Status == "succeeded")
                {
                    _logger.LogInformation("Payment confirmed successfully for {PaymentIntentId}", paymentIntentId);

                    return new PaymentResult
                    {
                        Success = true,
                        TransactionId = paymentIntent.Id,
                        Amount = paymentIntent.Amount / 100.0m, // Convert from cents
                        Currency = paymentIntent.Currency,
                        Status = PaymentStatus.Success,
                        Message = "Payment confirmed successfully"
                    };
                }
                else
                {
                    _logger.LogWarning("Payment confirmation failed for {PaymentIntentId}, Status: {Status}", 
                        paymentIntentId, paymentIntent.Status);

                    return new PaymentResult
                    {
                        Success = false,
                        TransactionId = paymentIntent.Id,
                        Amount = paymentIntent.Amount / 100.0m,
                        Currency = paymentIntent.Currency,
                        Status = PaymentStatus.Failed,
                        Message = $"Payment confirmation failed: {paymentIntent.Status}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming payment {PaymentIntentId}", paymentIntentId);

                return new PaymentResult
                {
                    Success = false,
                    Status = PaymentStatus.Failed,
                    Message = "Error confirming payment"
                };
            }
        }

        public async Task<PaymentResult> RefundPaymentAsync(string paymentIntentId, decimal? amount = null)
        {
            try
            {
                _logger.LogInformation("Processing refund for payment intent {PaymentIntentId}", paymentIntentId);

                var refundOptions = new RefundCreateOptions
                {
                    PaymentIntent = paymentIntentId
                };

                if (amount.HasValue)
                {
                    refundOptions.Amount = (long)(amount.Value * 100); // Convert to cents
                }

                var refundService = new RefundService();
                var refund = await refundService.CreateAsync(refundOptions);

                _logger.LogInformation("Refund processed successfully for {PaymentIntentId}, Refund ID: {RefundId}", 
                    paymentIntentId, refund.Id);

                return new PaymentResult
                {
                    Success = true,
                    TransactionId = refund.Id,
                    Amount = refund.Amount / 100.0m,
                    Currency = refund.Currency,
                    Status = PaymentStatus.Refunded,
                    Message = "Refund processed successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing refund for {PaymentIntentId}", paymentIntentId);

                return new PaymentResult
                {
                    Success = false,
                    Status = PaymentStatus.Failed,
                    Message = "Error processing refund"
                };
            }
        }

        public async Task<PaymentResult> GetPaymentStatusAsync(string paymentIntentId)
        {
            try
            {
                var paymentIntentService = new PaymentIntentService();
                var paymentIntent = await paymentIntentService.GetAsync(paymentIntentId);

                var status = paymentIntent.Status switch
                {
                    "succeeded" => PaymentStatus.Success,
                    "processing" => PaymentStatus.Processing,
                    "requires_payment_method" => PaymentStatus.Failed,
                    "requires_confirmation" => PaymentStatus.RequiresAction,
                    "requires_action" => PaymentStatus.RequiresAction,
                    "canceled" => PaymentStatus.Cancelled,
                    _ => PaymentStatus.Failed
                };

                return new PaymentResult
                {
                    Success = status == PaymentStatus.Success,
                    TransactionId = paymentIntent.Id,
                    Amount = paymentIntent.Amount / 100.0m,
                    Currency = paymentIntent.Currency,
                    Status = status,
                    Message = $"Payment status: {paymentIntent.Status}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payment status for {PaymentIntentId}", paymentIntentId);

                return new PaymentResult
                {
                    Success = false,
                    Status = PaymentStatus.Failed,
                    Message = "Error retrieving payment status"
                };
            }
        }

        public async Task<PaymentResult> RequestRefundAsync(int bookingId, int userId, RefundRequest refundRequest)
        {
            try
            {
                _logger.LogInformation("Requesting refund for booking {BookingId} by user {UserId}", bookingId, userId);

                // This is a placeholder implementation
                // In a real application, you would:
                // 1. Validate the booking belongs to the user
                // 2. Check if the booking is eligible for refund
                // 3. Process the refund through Stripe
                // 4. Update the booking status

                return new PaymentResult
                {
                    Success = true,
                    TransactionId = $"refund_{bookingId}_{DateTime.UtcNow.Ticks}",
                    Amount = refundRequest.Amount,
                    Currency = refundRequest.Currency,
                    Status = PaymentStatus.Refunded,
                    Message = "Refund request processed successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error requesting refund for booking {BookingId}", bookingId);

                return new PaymentResult
                {
                    Success = false,
                    Status = PaymentStatus.Failed,
                    Message = "Error processing refund request"
                };
            }
        }
    }
} 