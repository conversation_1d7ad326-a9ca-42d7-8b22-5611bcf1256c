using TravelTourism.Application.DTOs.Booking;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Core.Enums;

namespace TravelTourism.Application.Interfaces;

public interface IBookingService
{
    Task<PagedResult<BookingDto>> GetBookingsAsync(PaginationParameters parameters);
    Task<BookingDto?> GetBookingByIdAsync(int id);
    Task<BookingDto?> GetBookingByNumberAsync(string bookingNumber);
    Task<PagedResult<BookingDto>> GetUserBookingsAsync(int userId, PaginationParameters parameters);
    Task<PagedResult<BookingDto>> GetUserBookingsAsync(int userId, BookingStatus? status, int page, int pageSize);
    Task<BookingDto> CreateBookingAsync(CreateBookingRequest createBookingRequest, int userId);
    Task<BookingDto> UpdateBookingAsync(int id, UpdateBookingRequest updateBookingRequest);
    Task<bool> CancelBookingAsync(int id, string reason);
    Task<bool> ConfirmBookingAsync(int id);
    Task<bool> ProcessPaymentAsync(int bookingId, string paymentMethod, string transactionId);
    Task<List<BookingDto>> GetBookingsByStatusAsync(BookingStatus status);
    Task<BookingDto> GetBookingWithDetailsAsync(int id);
    
    // Additional methods needed by controllers
    Task<BookingDto?> GetBookingByIdAsync(int id, int userId);
    Task<bool> CancelBookingAsync(int id, int userId);
    Task<bool> ProcessPaymentAsync(int bookingId, string paymentMethod, string transactionId, int userId);
    Task<string> GetPaymentStatusAsync(int bookingId, int userId);
    Task<bool> ConfirmPaymentAsync(int bookingId, string transactionId, int userId);
    Task<bool> UpdateBookingAsync(int id, UpdateBookingRequest updateBookingRequest, int userId);
    Task<string> GenerateInvoiceAsync(int bookingId, int userId);
} 