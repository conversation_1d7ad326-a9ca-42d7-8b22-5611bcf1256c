using System.Threading.Tasks;
using TravelTourism.Core.Entities.User;

namespace TravelTourism.Core.Interfaces.Repositories
{
    public interface IUserRepository : IGenericRepository<User>
    {
        Task<User> GetByEmailAsync(string email);
        Task<User> GetByEmailVerificationTokenAsync(string token);
        Task<User> GetByPasswordResetTokenAsync(string token);
        Task<bool> IsEmailExistsAsync(string email);
        Task<UserToken> GetRefreshTokenAsync(string refreshToken);
        Task RevokeAllUserTokensAsync(int userId);
    }
}
