using Microsoft.EntityFrameworkCore;
using TravelTourism.Core.Entities.Common;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Repositories
{
    public class CityRepository : GenericRepository<City>, ICityRepository
    {
        public CityRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IReadOnlyList<City>> GetCitiesByCountryAsync(int countryId)
        {
            return await _context.Cities
                .Where(c => c.CountryId == countryId && c.IsActive && !c.IsDeleted)
                .Include(c => c.Country)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<City>> GetPopularCitiesAsync()
        {
            return await _context.Cities
                .Where(c => c.IsPopular && c.IsActive && !c.IsDeleted)
                .Include(c => c.Country)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<City>> GetActiveCitiesAsync()
        {
            return await _context.Cities
                .Where(c => c.IsActive && !c.IsDeleted)
                .Include(c => c.Country)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<City> GetCityWithCountryAsync(int id)
        {
            return await _context.Cities
                .Where(c => c.Id == id && c.IsActive && !c.IsDeleted)
                .Include(c => c.Country)
                .FirstOrDefaultAsync();
        }
    }
}
