# 🏖️ Travel & Tourism API

A comprehensive .NET 8 Web API for managing travel and tourism services, built with Clean Architecture principles and modern development practices.

## 🚀 Features

### 🔐 Authentication & Security
- **JWT-based Authentication** with secure token management
- **Email Verification** using SendGrid integration
- **Password Reset** functionality with secure tokens
- **Role-based Authorization** (Admin/User roles)
- **Rate Limiting** for API protection
- **CORS** configuration for cross-origin requests

### 🏗️ Core Entities
- **Users**: Registration, login, profile management
- **Trips**: Travel packages with categories, destinations, pricing
- **Blogs**: Travel articles with categories and tags
- **Bookings**: Trip reservations with payment integration
- **Countries/Cities**: Master data for destinations

### 👨‍💼 Admin Features
- Complete CRUD operations for trips and blogs
- User management and role administration
- Booking management and analytics
- Dashboard with comprehensive statistics
- System cache management

### 👤 User Features
- Browse and search trips with advanced filtering
- Read travel blogs and articles
- Book trips with secure payment processing
- Manage profile and booking history
- Email notifications for bookings

### ⚙️ Technical Features
- **Generic Repository** with Unit of Work pattern
- **Specifications pattern** for complex queries
- **File upload** with Cloudinary integration
- **Payment processing** with Stripe integration
- **Redis caching** for performance optimization
- **Comprehensive logging** with Serilog
- **API versioning** support
- **Health checks** for monitoring
- **Swagger documentation** with authentication

## 🛠️ Technology Stack

### Core Packages
- **.NET 8** - Latest framework version
- **Entity Framework Core** with SQL Server
- **AutoMapper** for object mapping
- **FluentValidation** for input validation
- **JWT Bearer** authentication
- **SendGrid** for email services
- **Cloudinary** for image storage
- **Stripe** for payments
- **Redis** for caching

### Development & Production
- **Swagger** for API documentation
- **Serilog** for structured logging
- **Health checks** for monitoring
- **Rate limiting** for security
- **API versioning** for backward compatibility

## 📁 Project Structure

```
TravelTourism/
├── TravelTourism.API/           # Web API layer
│   ├── Controllers/             # API endpoints
│   ├── Middleware/              # Custom middleware
│   ├── Filters/                 # Action filters
│   ├── Extensions/              # Extension methods
│   └── HealthChecks/            # Health check implementations
├── TravelTourism.Application/   # Application layer
│   ├── Services/                # Business logic services
│   ├── DTOs/                    # Data transfer objects
│   ├── Interfaces/              # Service contracts
│   ├── Validators/              # FluentValidation rules
│   └── Mappings/                # AutoMapper profiles
├── TravelTourism.Core/          # Domain layer
│   ├── Entities/                # Domain entities
│   ├── Interfaces/              # Repository contracts
│   ├── Specifications/          # Query specifications
│   ├── Enums/                   # Domain enums
│   └── ValueObjects/            # Value objects
└── TravelTourism.Infrastructure/ # Infrastructure layer
    ├── Data/                    # Entity Framework context
    ├── Repositories/            # Repository implementations
    ├── Services/                # External service implementations
    └── Specifications/          # Specification evaluator
```

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- SQL Server (LocalDB or full instance)
- Redis (optional, for caching)
- SendGrid account (for emails)
- Cloudinary account (for file storage)
- Stripe account (for payments)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd travel-tourism-api
   ```

2. **Configure the database**
   ```bash
   # Update connection string in appsettings.json
   "ConnectionStrings": {
     "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=TravelTourismDB;Trusted_Connection=true;MultipleActiveResultSets=true"
   }
   ```

3. **Run database migrations**
   ```bash
   cd TravelTourism.API
   dotnet ef database update
   ```

4. **Configure external services**
   Update `appsettings.json` with your service credentials:
   ```json
   {
     "JwtSettings": {
       "Key": "your-super-secret-key-here-make-it-long-and-complex",
       "Issuer": "TravelTourismAPI",
       "Audience": "TravelTourismAPI",
       "DurationInMinutes": 60
     },
     "EmailSettings": {
       "ApiKey": "your-sendgrid-api-key",
       "FromEmail": "<EMAIL>",
       "FromName": "Travel & Tourism"
     },
     "FileStorage": {
       "CloudinarySettings": {
         "CloudName": "your-cloudinary-cloud-name",
         "ApiKey": "your-cloudinary-api-key",
         "ApiSecret": "your-cloudinary-api-secret"
       }
     },
     "PaymentSettings": {
       "Stripe": {
         "PublishableKey": "your-stripe-publishable-key",
         "SecretKey": "your-stripe-secret-key"
       }
     },
     "Redis": {
       "ConnectionString": "localhost:6379"
     }
   }
   ```

5. **Run the application**
   ```bash
   dotnet run
   ```

6. **Access the API**
   - API: `https://localhost:7001`
   - Swagger UI: `https://localhost:7001`
   - Health Check: `https://localhost:7001/health`

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phoneNumber": "+**********"
}
```

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

#### Forgot Password
```http
POST /api/v1/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### Trip Endpoints

#### Get All Trips
```http
GET /api/v1/trips?page=1&pageSize=10&categoryId=1&searchTerm=beach
Authorization: Bearer <jwt-token>
```

#### Get Trip Details
```http
GET /api/v1/trips/{id}
Authorization: Bearer <jwt-token>
```

#### Create Trip (Admin Only)
```http
POST /api/v1/admin/trips
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "Beach Paradise",
  "description": "Amazing beach vacation",
  "categoryId": 1,
  "price": 999.99,
  "duration": 7,
  "maxPeople": 20,
  "destination": "Maldives"
}
```

### Booking Endpoints

#### Create Booking
```http
POST /api/v1/bookings
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "tripId": 1,
  "travelDate": "2024-07-15",
  "numberOfAdults": 2,
  "numberOfChildren": 1,
  "specialRequests": "Vegetarian meals"
}
```

#### Process Payment
```http
POST /api/v1/bookings/{id}/payment
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "paymentMethodId": "pm_card_visa"
}
```

### Blog Endpoints

#### Get All Blogs
```http
GET /api/v1/blogs?page=1&pageSize=10&categoryId=1&isPublished=true
```

#### Get Blog Details
```http
GET /api/v1/blogs/{id}
```

#### Create Blog (Admin Only)
```http
POST /api/v1/admin/blogs
Authorization: Bearer <jwt-token>
Content-Type: multipart/form-data

{
  "title": "Top 10 Beach Destinations",
  "content": "Amazing beach destinations...",
  "categoryId": 1,
  "featuredImage": <file>
}
```

### Admin Endpoints

#### Dashboard Stats
```http
GET /api/v1/admin/dashboard/stats
Authorization: Bearer <jwt-token>
```

#### Get All Users
```http
GET /api/v1/admin/users?page=1&pageSize=20&role=User&isActive=true
Authorization: Bearer <jwt-token>
```

#### Manage User
```http
PUT /api/v1/admin/users/{id}/activate
Authorization: Bearer <jwt-token>
```

## 🔧 Configuration

### Environment Variables
- `ASPNETCORE_ENVIRONMENT`: Set to `Development`, `Staging`, or `Production`
- `ConnectionStrings__DefaultConnection`: Database connection string
- `JwtSettings__Key`: JWT secret key
- `EmailSettings__ApiKey`: SendGrid API key
- `FileStorage__CloudinarySettings__CloudName`: Cloudinary cloud name
- `PaymentSettings__Stripe__SecretKey`: Stripe secret key
- `Redis__ConnectionString`: Redis connection string

### Rate Limiting
The API implements rate limiting with the following policies:
- **Global**: 100 requests per minute
- **Authenticated Users**: 1000 requests per minute
- **Authentication Endpoints**: 10 requests per minute

### Caching
- **Redis**: Used for session storage and data caching
- **Cache Duration**: Varies by data type (5 minutes to 1 hour)
- **Cache Invalidation**: Automatic on data updates

## 🧪 Testing

### Unit Tests
```bash
dotnet test
```

### Integration Tests
```bash
dotnet test --filter Category=Integration
```

### API Tests
Use the provided `TravelTourism.API.http` file for testing endpoints in VS Code or JetBrains Rider.

## 📊 Monitoring

### Health Checks
- **Database**: `GET /health`
- **External Services**: `GET /health`
- **Custom Metrics**: Available in dashboard

### Logging
- **Structured Logging**: Using Serilog
- **Log Levels**: Information, Warning, Error
- **Log Storage**: Console and file output
- **Log Retention**: 30 days for file logs

## 🚀 Deployment

### Docker
```bash
docker build -t travel-tourism-api .
docker run -p 8080:80 travel-tourism-api
```

### Azure
```bash
az webapp up --name travel-tourism-api --resource-group myResourceGroup --runtime "DOTNETCORE:8.0"
```

### AWS
```bash
dotnet publish -c Release
aws elasticbeanstalk create-application-version --application-name travel-tourism-api --version-label v1 --source-bundle S3Bucket="my-bucket",S3Key="travel-tourism-api.zip"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the API documentation at `/swagger`

## 🔄 Version History

- **v1.0.0** - Initial release with core features
- **v1.1.0** - Added payment processing and admin dashboard
- **v1.2.0** - Enhanced caching and performance optimizations
- **v1.3.0** - Added comprehensive logging and monitoring

---

**Built with ❤️ using .NET 8 and Clean Architecture principles**
