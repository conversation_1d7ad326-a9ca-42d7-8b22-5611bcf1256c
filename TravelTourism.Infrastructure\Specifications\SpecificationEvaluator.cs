using Microsoft.EntityFrameworkCore;
using System.Linq;
using TravelTourism.Core.Entities.Base;
using TravelTourism.Core.Interfaces;

namespace TravelTourism.Infrastructure.Specifications
{
    public class SpecificationEvaluator<TEntity> where TEntity : BaseEntity
    {
        public static IQueryable<TEntity> GetQuery(IQueryable<TEntity> inputQuery, ISpecification<TEntity> specification)
        {
            var query = inputQuery;

            // Apply criteria (where clause)
            if (specification.Criteria != null)
            {
                query = query.Where(specification.Criteria);
            }

            // Include any related entities
            query = specification.Includes.Aggregate(query,
                (current, include) => current.Include(include));

            // Include any string-based includes
            query = specification.IncludeStrings.Aggregate(query,
                (current, include) => current.Include(include));

            // Apply ordering
            if (specification.OrderBy != null)
            {
                query = query.OrderBy(specification.OrderBy);
            }
            else if (specification.OrderByDescending != null)
            {
                query = query.OrderByDescending(specification.OrderByDescending);
            }

            // Apply grouping
            if (specification.GroupBy != null)
            {
                query = query.GroupBy(specification.GroupBy).SelectMany(x => x);
            }

            // Apply paging
            if (specification.IsPagingEnabled)
            {
                query = query.Skip(specification.Skip).Take(specification.Take);
            }

            return query;
        }
    }
}
