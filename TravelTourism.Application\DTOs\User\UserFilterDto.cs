using TravelTourism.Core.Enums;
using TravelTourism.Application.DTOs.Common;

namespace TravelTourism.Application.DTOs.User
{
    public class UserFilterDto : PaginationParameters
    {
        public UserRole? Role { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsEmailVerified { get; set; }
        public string? SearchTerm { get; set; }
        public DateTime? CreatedFrom { get; set; }
        public DateTime? CreatedTo { get; set; }
        public DateTime? LastLoginFrom { get; set; }
        public DateTime? LastLoginTo { get; set; }
        public int Page { get; set; } = 1;
    }
} 