using Microsoft.EntityFrameworkCore;
using TravelTourism.Core.Entities.Common;
using TravelTourism.Core.Interfaces.Repositories;
using TravelTourism.Infrastructure.Data;

namespace TravelTourism.Infrastructure.Repositories
{
    public class CountryRepository : GenericRepository<Country>, ICountryRepository
    {
        public CountryRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IReadOnlyList<Country>> GetActiveCountriesAsync()
        {
            return await _context.Countries
                .Where(c => c.IsActive && !c.IsDeleted)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Country> GetByCodeAsync(string code)
        {
            return await _context.Countries
                .FirstOrDefaultAsync(c => c.Code == code && c.IsActive && !c.IsDeleted);
        }

        public async Task<Country> GetCountryWithCitiesAsync(int id)
        {
            return await _context.Countries
                .Where(c => c.Id == id && c.IsActive && !c.Is<PERSON>eleted)
                .Include(c => c.Cities.Where(city => city.IsActive))
                .FirstOrDefaultAsync();
        }
    }
}
