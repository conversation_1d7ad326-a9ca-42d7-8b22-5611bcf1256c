using System;
using TravelTourism.Core.Entities.Base;
using TravelTourism.Core.Enums;

namespace TravelTourism.Core.Entities.Booking
{
    public class BookingPayment : BaseEntity
    {
        public int BookingId { get; set; }
        public string PaymentMethod { get; set; }
        public string TransactionId { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;
        public DateTime? PaymentDate { get; set; }
        public string FailureReason { get; set; }
        public decimal? RefundAmount { get; set; }
        public DateTime? RefundDate { get; set; }

        // Navigation properties
        public virtual Booking Booking { get; set; }

        public bool IsSuccessful => Status == PaymentStatus.Paid;
        public bool IsFailed => Status == PaymentStatus.Failed;
        public bool IsRefunded => Status == PaymentStatus.Refunded;

        public void MarkAsSuccessful(string transactionId)
        {
            Status = PaymentStatus.Paid;
            TransactionId = transactionId;
            PaymentDate = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }

        public void MarkAsFailed(string reason)
        {
            Status = PaymentStatus.Failed;
            FailureReason = reason;
            UpdatedAt = DateTime.UtcNow;
        }

        public void ProcessRefund(decimal refundAmount)
        {
            if (Status != PaymentStatus.Paid)
                throw new InvalidOperationException("Can only refund successful payments");

            Status = PaymentStatus.Refunded;
            RefundAmount = refundAmount;
            RefundDate = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
