using System;
using System.Threading.Tasks;
using AutoMapper;
using TravelTourism.Application.DTOs.User;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.DTOs.Booking;
using TravelTourism.Application.Interfaces;
using TravelTourism.Core.Entities.User;
using TravelTourism.Core.Exceptions;
using TravelTourism.Core.Interfaces;
using Microsoft.AspNetCore.Http;
using System.IO;
using TravelTourism.Core.Interfaces.Services;
using System.Collections.Generic;
using System.Linq;

namespace TravelTourism.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IFileStorageService _fileStorageService;

        public UserService(
            IMapper mapper,
            IUnitOfWork unitOfWork,
            IFileStorageService fileStorageService)
        {
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            _fileStorageService = fileStorageService;
        }

        public async Task<UserProfileDto> GetProfileAsync(string userId)
        {
            if (!int.TryParse(userId, out var userIdInt))
            {
                throw new NotFoundException("Invalid user ID");
            }

            var user = await _unitOfWork.Users.GetByIdAsync(userIdInt);
            if (user == null)
            {
                throw new NotFoundException("User not found");
            }

            return _mapper.Map<UserProfileDto>(user);
        }

        public async Task UpdateProfileAsync(string userId, UpdateUserProfileDto userProfileDto)
        {
            if (!int.TryParse(userId, out var userIdInt))
            {
                throw new NotFoundException("Invalid user ID");
            }

            var user = await _unitOfWork.Users.GetByIdAsync(userIdInt);
            if (user == null)
            {
                throw new NotFoundException("User not found");
            }

            // Map the updated fields
            user.FirstName = userProfileDto.FirstName;
            user.LastName = userProfileDto.LastName;
            user.PhoneNumber = userProfileDto.PhoneNumber;
            user.DateOfBirth = userProfileDto.DateOfBirth;

            // Parse Gender enum if provided
            if (!string.IsNullOrEmpty(userProfileDto.Gender) && Enum.TryParse<TravelTourism.Core.Enums.Gender>(userProfileDto.Gender, true, out var genderEnum))
            {
                user.Gender = genderEnum;
            }

            user.City = userProfileDto.City;
            user.Country = userProfileDto.Country;
            user.UpdatedAt = DateTime.UtcNow;

            // Handle profile image upload if provided
            if (userProfileDto.ProfileImage != null)
            {
                // Delete existing profile image if it exists
                if (!string.IsNullOrEmpty(user.ProfileImageUrl))
                {
                    await _fileStorageService.DeleteFileAsync(user.ProfileImageUrl);
                }

                // Upload new profile image
                using var imageStream = userProfileDto.ProfileImage.OpenReadStream();
                var imageUrl = await _fileStorageService.UploadImageAsync(imageStream, userProfileDto.ProfileImage.FileName, "profile-images");
                user.ProfileImageUrl = imageUrl;
            }

            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();
        }

        public async Task DeleteAccountAsync(string userId)
        {
            if (!int.TryParse(userId, out var userIdInt))
            {
                throw new NotFoundException("Invalid user ID");
            }

            var user = await _unitOfWork.Users.GetByIdAsync(userIdInt);
            if (user == null)
            {
                throw new NotFoundException("User not found");
            }

            // Delete profile image from cloud storage if it exists
            if (!string.IsNullOrEmpty(user.ProfileImageUrl))
            {
                await _fileStorageService.DeleteFileAsync(user.ProfileImageUrl);
            }

            // Soft delete user account
            user.IsDeleted = true;
            user.UpdatedAt = DateTime.UtcNow;
            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();
        }

        // Additional methods needed by controllers
        public async Task<UserDto> GetUserByIdAsync(int userId)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                throw new NotFoundException("User not found");
            }

            return _mapper.Map<UserDto>(user);
        }

        public async Task<bool> UpdateUserAsync(int userId, UpdateUserDto updateUserDto)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            // Update user properties
            user.FirstName = updateUserDto.FirstName ?? user.FirstName;
            user.LastName = updateUserDto.LastName ?? user.LastName;
            user.PhoneNumber = updateUserDto.PhoneNumber ?? user.PhoneNumber;
            user.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            user.IsDeleted = true;
            user.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<PagedResult<BookingDto>> GetUserBookingsAsync(int userId, PaginationParameters parameters, DateTime? startDate = null, DateTime? endDate = null)
        {
            var allBookings = await _unitOfWork.Bookings.GetAllAsync();
            
            // Filter by user
            var userBookings = allBookings.Where(b => b.UserId == userId && !b.IsDeleted).ToList();

            // Apply date filters if provided
            if (startDate.HasValue)
            {
                userBookings = userBookings.Where(b => b.CreatedAt >= startDate.Value).ToList();
            }

            if (endDate.HasValue)
            {
                userBookings = userBookings.Where(b => b.CreatedAt <= endDate.Value).ToList();
            }

            // Apply pagination
            var totalCount = userBookings.Count;
            var pagedBookings = userBookings
                .Skip(parameters.Skip)
                .Take(parameters.Take)
                .ToList();

            var bookingDtos = _mapper.Map<List<BookingDto>>(pagedBookings);
            return PagedResult<BookingDto>.Create(bookingDtos, totalCount, parameters.PageNumber, parameters.PageSize);
        }
    }
}
