using TravelTourism.Core.Entities.Base;

namespace TravelTourism.Core.Entities.Trip
{
    public class TripItinerary : BaseEntity
    {
        public int TripId { get; set; }
        public int Day { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Activities { get; set; }
        public string Meals { get; set; }
        public string Accommodation { get; set; }

        // Navigation properties
        public virtual Trip Trip { get; set; }
    }
}
